/**
 * 穷尽式MD5搜索 - 尝试更多可能的输入组合
 */

const crypto = require('crypto');

// 目标MD5哈希
const targets = [
    { number: 8196230, hex: "f7b696b02b26f788bdf6886107021d22" },
    { number: 8199733, hex: "df3c044c41c841607f3b757698cdc34c" },
    { number: 8201579, hex: "f19e3cc9c35c9596269199d70b246c18" }
];

const contextData = {
    ch: "bx65qqd",
    hqs: "fdj", 
    upuid: 8196926,
    xtp: "0kp"
};

// 可能的服务端密钥/盐值
const possibleSecrets = [
    'pigxpigxpigxpigx',
    'server_secret_key',
    'iu_generation_salt',
    'task_encryption_key',
    'auth_secret_2024',
    'md5_salt_key',
    'user_token_salt',
    'api_secret_key',
    'login_hash_salt',
    'secure_key_2024',
    // 基于上下文的组合
    contextData.ch + contextData.hqs,
    contextData.ch + contextData.upuid,
    contextData.hqs + contextData.upuid,
    // 可能的时间相关
    '20241228',
    '2024',
    '1228',
    // 数字相关
    '123456',
    '888888',
    '666666'
];

// 可能的格式模板
const formatTemplates = [
    // 基础格式
    '{number}',
    '{secret}{number}',
    '{number}{secret}',
    '{secret}_{number}',
    '{number}_{secret}',
    '{secret}|{number}',
    '{number}|{secret}',
    '{secret}:{number}',
    '{number}:{secret}',
    '{secret}#{number}',
    '{number}#{secret}',
    
    // 带上下文
    '{number}_{ch}',
    '{ch}_{number}',
    '{number}_{upuid}',
    '{upuid}_{number}',
    '{number}_{ch}_{upuid}',
    '{upuid}_{number}_{ch}',
    '{ch}_{upuid}_{number}',
    
    // 带密钥和上下文
    '{secret}_{number}_{ch}',
    '{secret}_{ch}_{number}',
    '{number}_{secret}_{ch}',
    '{number}_{ch}_{secret}',
    '{ch}_{secret}_{number}',
    '{ch}_{number}_{secret}',
    
    // 复杂格式
    '{secret}_{number}_{ch}_{upuid}',
    '{number}_{secret}_{ch}_{upuid}',
    'iu_{number}_{secret}',
    'task_{number}_{secret}',
    'user_{number}_{secret}',
    'auth_{number}_{secret}',
    'login_{number}_{secret}',
    
    // 数学运算
    '{number_minus_upuid}_{secret}',
    '{number_plus_upuid}_{secret}',
    '{secret}_{number_minus_upuid}',
    '{secret}_{number_plus_upuid}'
];

function generateInput(template, number, secret, contextData) {
    return template
        .replace('{number}', number)
        .replace('{secret}', secret)
        .replace('{ch}', contextData.ch)
        .replace('{upuid}', contextData.upuid)
        .replace('{hqs}', contextData.hqs)
        .replace('{xtp}', contextData.xtp)
        .replace('{number_minus_upuid}', number - contextData.upuid)
        .replace('{number_plus_upuid}', number + contextData.upuid);
}

function searchMD5Match() {
    console.log("=== 穷尽式MD5搜索 ===");
    
    let totalAttempts = 0;
    const maxAttemptsPerTarget = 1000; // 限制每个目标的尝试次数
    
    targets.forEach((target, targetIndex) => {
        console.log(`\n--- 搜索目标 ${targetIndex + 1}: ${target.number} ---`);
        console.log(`目标MD5: ${target.hex}`);
        
        let found = false;
        let attempts = 0;
        
        outerLoop: for (const secret of possibleSecrets) {
            for (const template of formatTemplates) {
                if (attempts >= maxAttemptsPerTarget) {
                    console.log(`达到最大尝试次数限制 (${maxAttemptsPerTarget})`);
                    break outerLoop;
                }
                
                try {
                    const input = generateInput(template, target.number, secret, contextData);
                    const md5Hash = crypto.createHash('md5').update(input).digest('hex');
                    
                    attempts++;
                    totalAttempts++;
                    
                    if (md5Hash === target.hex) {
                        console.log(`🎯 找到匹配！`);
                        console.log(`  模板: ${template}`);
                        console.log(`  密钥: ${secret}`);
                        console.log(`  输入: ${input}`);
                        console.log(`  MD5: ${md5Hash}`);
                        console.log(`  Base64: ${crypto.createHash('md5').update(input).digest('base64')}`);
                        found = true;
                        break outerLoop;
                    }
                    
                    // 每100次尝试显示进度
                    if (attempts % 100 === 0) {
                        console.log(`  已尝试 ${attempts} 种组合...`);
                    }
                    
                } catch (error) {
                    // 忽略错误，继续尝试
                }
            }
        }
        
        if (!found) {
            console.log(`❌ 在 ${attempts} 次尝试中未找到匹配`);
            
            // 显示一些尝试的例子
            console.log(`最后几次尝试的结果:`);
            const lastSecret = possibleSecrets[possibleSecrets.length - 1];
            const lastTemplates = formatTemplates.slice(-3);
            
            lastTemplates.forEach((template, i) => {
                try {
                    const input = generateInput(template, target.number, lastSecret, contextData);
                    const md5Hash = crypto.createHash('md5').update(input).digest('hex');
                    console.log(`    "${input}" -> ${md5Hash}`);
                } catch (e) {
                    // 忽略错误
                }
            });
        }
    });
    
    console.log(`\n总共尝试了 ${totalAttempts} 种组合`);
}

// 尝试基于时间的输入
function tryTimeBasedInputs() {
    console.log("\n=== 基于时间的输入尝试 ===");
    
    // 生成可能的时间戳
    const now = Math.floor(Date.now() / 1000);
    const timeVariants = [
        now,
        now - 3600, // 1小时前
        now - 86400, // 1天前
        now - 604800, // 1周前
        Math.floor(now / 3600), // 小时级时间戳
        Math.floor(now / 86400), // 天级时间戳
        Math.floor(now / 604800), // 周级时间戳
        // 固定的时间戳（可能是系统部署时间等）
        1703721600, // 2023-12-28 00:00:00
        1704067200, // 2024-01-01 00:00:00
        1735689600  // 2025-01-01 00:00:00
    ];
    
    targets.forEach((target, targetIndex) => {
        console.log(`\n--- 时间相关搜索: ${target.number} ---`);
        
        let found = false;
        
        timeVariants.forEach(timestamp => {
            const timeInputs = [
                `${target.number}_${timestamp}`,
                `${timestamp}_${target.number}`,
                `${target.number}|${timestamp}`,
                `${timestamp}|${target.number}`,
                `iu_${target.number}_${timestamp}`,
                `task_${target.number}_${timestamp}`,
                `${target.number}_${timestamp}_${contextData.ch}`
            ];
            
            timeInputs.forEach(input => {
                const md5Hash = crypto.createHash('md5').update(input).digest('hex');
                if (md5Hash === target.hex) {
                    console.log(`🎯 时间相关匹配！`);
                    console.log(`  输入: ${input}`);
                    console.log(`  时间戳: ${timestamp}`);
                    console.log(`  时间: ${new Date(timestamp * 1000).toISOString()}`);
                    console.log(`  MD5: ${md5Hash}`);
                    found = true;
                }
            });
        });
        
        if (!found) {
            console.log(`❌ 时间相关输入未找到匹配`);
        }
    });
}

// 尝试数字序列相关的输入
function trySequenceBasedInputs() {
    console.log("\n=== 基于数字序列的输入尝试 ===");
    
    targets.forEach((target, targetIndex) => {
        console.log(`\n--- 序列相关搜索: ${target.number} ---`);
        
        // 分析数字的特征
        const numStr = target.number.toString();
        const reversed = numStr.split('').reverse().join('');
        const sum = numStr.split('').reduce((a, b) => parseInt(a) + parseInt(b), 0);
        
        const sequenceInputs = [
            `${target.number}_${reversed}`,
            `${reversed}_${target.number}`,
            `${target.number}_${sum}`,
            `${sum}_${target.number}`,
            `${target.number}_${numStr.length}`,
            `${numStr.length}_${target.number}`,
            // 可能的序列号
            `seq_${target.number}`,
            `${target.number}_seq`,
            `id_${target.number}`,
            `${target.number}_id`,
            `uid_${target.number}`,
            `${target.number}_uid`
        ];
        
        let found = false;
        
        sequenceInputs.forEach(input => {
            const md5Hash = crypto.createHash('md5').update(input).digest('hex');
            if (md5Hash === target.hex) {
                console.log(`🎯 序列相关匹配！`);
                console.log(`  输入: ${input}`);
                console.log(`  MD5: ${md5Hash}`);
                found = true;
            }
        });
        
        if (!found) {
            console.log(`❌ 序列相关输入未找到匹配`);
        }
    });
}

// 运行所有搜索
console.log("开始穷尽式搜索...");
searchMD5Match();
tryTimeBasedInputs();
trySequenceBasedInputs();

console.log("\n=== 搜索完成 ===");
console.log("如果没有找到匹配，可能的原因：");
console.log("1. 服务端使用了我们没有考虑到的密钥");
console.log("2. 输入格式与我们的模板不匹配");
console.log("3. 使用了其他哈希算法（虽然16字节强烈暗示MD5）");
console.log("4. 包含了动态数据（如精确的时间戳、随机数等）");
console.log("5. 使用了更复杂的编码或预处理步骤");
