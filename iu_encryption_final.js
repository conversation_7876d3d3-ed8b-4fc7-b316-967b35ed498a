/**
 * localStorage中'iu'的加密方法 - 最终版本
 * 基于对3组数据的深入分析
 */

// 加密字符串的结构分析
const ENCRYPTION_PATTERN = {
    prefix: "iub0c4d3p2",
    suffix: "2", 
    middleLength: 22,
    totalLength: 41
};

/**
 * 解析加密字符串的结构
 * @param {string} encryptedString - 完整的加密字符串
 * @returns {object} 解析结果
 */
function parseEncryptedString(encryptedString) {
    if (encryptedString.length !== ENCRYPTION_PATTERN.totalLength) {
        throw new Error(`加密字符串长度不正确，期望${ENCRYPTION_PATTERN.totalLength}位，实际${encryptedString.length}位`);
    }
    
    const prefix = encryptedString.substring(0, 10);
    const middle = encryptedString.substring(10, encryptedString.length - 1);
    const suffix = encryptedString.slice(-1);
    
    if (prefix !== ENCRYPTION_PATTERN.prefix || suffix !== ENCRYPTION_PATTERN.suffix) {
        throw new Error("加密字符串格式不正确");
    }
    
    // 尝试Base64解码中间部分
    let decoded = null;
    try {
        decoded = atob(middle);
    } catch (e) {
        console.warn("Base64解码失败:", e.message);
    }
    
    return {
        prefix,
        middle,
        suffix,
        decoded,
        isValid: true
    };
}

/**
 * 验证加密字符串是否符合预期格式
 * @param {string} encryptedString - 要验证的字符串
 * @returns {boolean} 是否有效
 */
function validateEncryptedString(encryptedString) {
    try {
        const parsed = parseEncryptedString(encryptedString);
        return parsed.isValid;
    } catch (e) {
        return false;
    }
}

/**
 * 构造加密字符串（需要服务器端支持）
 * 注意：中间部分的生成算法需要服务器端实现
 * @param {number} number - 要加密的数字
 * @param {object} contextData - 上下文数据
 * @returns {string} 构造的加密字符串格式
 */
function constructEncryptedStringTemplate(number, contextData = {}) {
    const prefix = ENCRYPTION_PATTERN.prefix;
    const suffix = ENCRYPTION_PATTERN.suffix;
    const placeholder = "[SERVER_GENERATED_HASH]";
    
    return {
        template: `${prefix}${placeholder}${suffix}`,
        note: "中间部分需要通过服务器API获取",
        requiredApiCall: `/task/login3?code=${number}&ch=${contextData.ch || ''}&upuid=${contextData.upuid || ''}`,
        expectedResponse: {
            code: 0,
            data: {
                iu: "完整的加密字符串"
            }
        }
    };
}

/**
 * 从API响应中提取'iu'值
 * @param {object} apiResponse - API响应对象
 * @returns {string|null} 提取的iu值
 */
function extractIuFromApiResponse(apiResponse) {
    if (apiResponse && apiResponse.code === 0 && apiResponse.data && apiResponse.data.iu) {
        const iu = apiResponse.data.iu;
        if (validateEncryptedString(iu)) {
            return iu;
        } else {
            console.warn("API返回的iu格式不正确:", iu);
        }
    }
    return null;
}

/**
 * 完整的'iu'获取和存储流程
 * @param {number} number - 数字参数
 * @param {object} contextData - 上下文数据
 * @returns {Promise<string>} 返回iu值
 */
async function getAndStoreIu(number, contextData = {}) {
    // 1. 检查localStorage中是否已存在
    const existingIu = localStorage.getItem("iu");
    if (existingIu && validateEncryptedString(existingIu)) {
        console.log("使用已存在的iu:", existingIu);
        return existingIu;
    }
    
    // 2. 构造API请求
    const baseUrl = getBaseUrl();
    const params = new URLSearchParams({
        code: number,
        ch: contextData.ch || '',
        upuid: contextData.upuid || '',
        hqs: contextData.hqs || '',
        xtp: contextData.xtp || ''
    });
    
    const apiUrl = `${baseUrl}/task/login3?${params.toString()}`;
    
    try {
        // 3. 调用API获取iu
        const response = await fetch(apiUrl);
        const data = await response.json();
        
        // 4. 提取并验证iu
        const iu = extractIuFromApiResponse(data);
        if (iu) {
            // 5. 存储到localStorage
            localStorage.setItem("iu", iu);
            console.log("成功获取并存储iu:", iu);
            return iu;
        } else {
            throw new Error(data.msg || "获取iu失败");
        }
    } catch (error) {
        console.error("获取iu时发生错误:", error);
        throw error;
    }
}

/**
 * 获取baseUrl
 * @returns {string} baseUrl
 */
function getBaseUrl() {
    const hostname = location.href;
    if (hostname.indexOf("myqcloud.com") > -1 || 
        hostname.indexOf("ctyun.cn") > -1 || 
        hostname.indexOf("cmecloud.cn") > -1 || 
        hostname.indexOf("ksyuncs.com") > -1) {
        return "https://m.chchapi.cn";
    } else {
        return "http://" + location.host;
    }
}

// 测试数据验证
const testCases = [
    { number: 8196230, encrypted: "iub0c4d3p2OTdhV3NDc205NGk5OW9oaEJ3SWRJZw2" },
    { number: 8199733, encrypted: "iub0c4d3p2M3p3RVRFSElRV0JfTzNWMm1NM0RUTQ2" },
    { number: 8201579, encrypted: "iub0c4d3p2OFo0OHljTmNsWllta1puWEN5UnNHRQ2" }
];

console.log("=== 测试加密字符串解析 ===");
testCases.forEach((testCase, index) => {
    console.log(`\n测试案例 ${index + 1}:`);
    console.log(`数字: ${testCase.number}`);
    console.log(`加密字符串: ${testCase.encrypted}`);
    
    try {
        const parsed = parseEncryptedString(testCase.encrypted);
        console.log(`解析结果:`, parsed);
        console.log(`验证通过: ${validateEncryptedString(testCase.encrypted)}`);
    } catch (error) {
        console.error(`解析失败:`, error.message);
    }
});

// 导出主要函数
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        parseEncryptedString,
        validateEncryptedString,
        constructEncryptedStringTemplate,
        extractIuFromApiResponse,
        getAndStoreIu,
        getBaseUrl,
        ENCRYPTION_PATTERN
    };
}
