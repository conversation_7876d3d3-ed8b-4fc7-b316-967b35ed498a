/**
 * 真实API数据分析
 * 基于实际请求数据进行逆向工程
 */

const crypto = require('crypto');

console.log("=== 真实API数据分析 ===");

// 从API请求中发现的iu值
const realIuValue = "iuMjgwNDQyMA2";
const realUid = "2804420";

console.log(`发现的iu值: ${realIuValue}`);
console.log(`对应的uid: ${realUid}`);

// 分析这个iu值的结构
function analyzeRealIu() {
    console.log("\n--- 真实iu值结构分析 ---");
    
    // 检查是否符合我们之前发现的模式
    if (realIuValue.startsWith("iu") && realIuValue.endsWith("2")) {
        console.log("✅ 符合 iu[Base64]2 格式");
        
        const middle = realIuValue.substring(2, realIuValue.length - 1);
        console.log(`中间部分: ${middle}`);
        console.log(`中间部分长度: ${middle.length}`);
        
        try {
            const decoded = atob(middle);
            console.log(`Base64解码: ${decoded}`);
            console.log(`解码长度: ${decoded.length}`);
            
            // 检查解码结果是否就是uid
            if (decoded === realUid) {
                console.log("🎯 重大发现：解码结果就是uid！");
                console.log("这意味着iu的生成逻辑是：iu + Base64(uid) + 2");
                return true;
            } else {
                console.log(`解码结果与uid不匹配: "${decoded}" vs "${realUid}"`);
            }
            
        } catch (e) {
            console.log(`Base64解码失败: ${e.message}`);
        }
    } else {
        console.log("❌ 不符合预期的iu格式");
    }
    
    return false;
}

// 验证我们的发现
function verifyDiscovery() {
    console.log("\n--- 验证发现的规律 ---");
    
    // 如果规律是 iu + Base64(uid) + 2，那么我们可以验证
    function generateIuFromUid(uid) {
        const base64Uid = btoa(uid.toString());
        return `iu${base64Uid}2`;
    }
    
    const generatedIu = generateIuFromUid(realUid);
    console.log(`根据uid生成的iu: ${generatedIu}`);
    console.log(`实际的iu值: ${realIuValue}`);
    console.log(`匹配: ${generatedIu === realIuValue ? '✅' : '❌'}`);
    
    if (generatedIu === realIuValue) {
        console.log("\n🎉 成功！iu的生成逻辑就是：iu + Base64(uid) + 2");
        
        // 验证之前的数据
        console.log("\n--- 验证之前的数据 ---");
        const previousData = [
            { number: 8196230, encrypted: "iub0c4d3p2OTdhV3NDc205NGk5OW9oaEJ3SWRJZw2" },
            { number: 8199733, encrypted: "iub0c4d3p2M3p3RVRFSElRV0JfTzNWMm1NM0RUTQ2" },
            { number: 8201579, encrypted: "iub0c4d3p2OFo0OHljTmNsWllta1puWEN5UnNHRQ2" }
        ];
        
        console.log("之前的数据使用了不同的格式：iub0c4d3p2[复杂Base64]2");
        console.log("而真实API使用的是简单格式：iu[Base64(uid)]2");
        console.log("这说明可能有两种不同的iu生成方式！");
        
        return true;
    }
    
    return false;
}

// 如果简单规律不匹配，尝试其他可能
function tryAlternativePatterns() {
    console.log("\n--- 尝试其他模式 ---");
    
    const middle = realIuValue.substring(2, realIuValue.length - 1);
    
    // 尝试不同的解码方式
    const decodingAttempts = [
        // 标准Base64
        () => atob(middle),
        
        // URL安全Base64
        () => {
            const urlSafe = middle.replace(/-/g, '+').replace(/_/g, '/');
            return atob(urlSafe);
        },
        
        // 可能的填充问题
        () => {
            const padded = middle + '='.repeat((4 - middle.length % 4) % 4);
            return atob(padded);
        }
    ];
    
    decodingAttempts.forEach((attempt, index) => {
        try {
            const decoded = attempt();
            console.log(`解码方式${index + 1}: "${decoded}"`);
            
            // 检查与uid的关系
            if (decoded === realUid) {
                console.log(`  ✅ 与uid完全匹配！`);
            } else if (decoded.includes(realUid)) {
                console.log(`  ✅ 包含uid！`);
            } else {
                // 尝试数字转换
                const asNumber = parseInt(decoded);
                if (!isNaN(asNumber)) {
                    console.log(`  作为数字: ${asNumber}`);
                    console.log(`  与uid的差: ${asNumber - parseInt(realUid)}`);
                }
            }
        } catch (e) {
            console.log(`解码方式${index + 1}: 失败 - ${e.message}`);
        }
    });
}

// 运行分析
const isSimplePattern = analyzeRealIu();

if (isSimplePattern) {
    verifyDiscovery();
} else {
    tryAlternativePatterns();
}

// 提供最终的生成函数
function provideFinalGenerationFunction() {
    console.log("\n=== 最终生成函数 ===");
    
    console.log(`
基于真实API数据的发现，iu的生成逻辑可能有两种：

1. 简单格式（真实API中发现的）：
   格式：iu + Base64(uid) + 2
   
2. 复杂格式（之前分析的数据）：
   格式：iub0c4d3p2 + 复杂Base64哈希 + 2

实现代码：
`);
    
    const implementationCode = `
// 简单格式生成（基于真实API发现）
function generateSimpleIu(uid) {
    const base64Uid = btoa(uid.toString());
    return 'iu' + base64Uid + '2';
}

// 复杂格式生成（需要服务端支持）
function generateComplexIu(number, serverSecret) {
    // 这部分需要服务端的具体实现
    const input = constructServerInput(number, serverSecret);
    const md5Hash = crypto.createHash('md5').update(input).digest('base64');
    return 'iub0c4d3p2' + md5Hash + '2';
}

// 使用示例
const uid = "2804420";
const simpleIu = generateSimpleIu(uid);
console.log('生成的iu:', simpleIu); // iu + Base64("2804420") + 2

// 验证
console.log('验证:', simpleIu === "iuMjgwNDQyMA2");
`;
    
    console.log(implementationCode);
}

provideFinalGenerationFunction();
