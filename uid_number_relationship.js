/**
 * 探索uid与长格式iu中数字的关系
 * 基于真实API中发现uid=2804420，尝试找出与8199733等数字的关系
 */

const crypto = require('crypto');

// 已知数据
const realUid = "2804420"; // 从真实API中发现
const longIuNumbers = [8196230, 8199733, 8201579]; // 长格式iu对应的数字
const longIuHashes = [
    "f7b696b02b26f788bdf6886107021d22",
    "df3c044c41c841607f3b757698cdc34c", 
    "f19e3cc9c35c9596269199d70b246c18"
];

const contextData = {
    ch: "bx65qqd",
    hqs: "fdj", 
    upuid: 8196926,
    xtp: "0kp"
};

console.log("=== uid与长格式iu数字关系探索 ===");
console.log(`真实uid: ${realUid}`);
console.log(`长格式数字: ${longIuNumbers.join(', ')}`);

// 分析数字关系
function analyzeNumberRelationships() {
    console.log("\n--- 数字关系分析 ---");
    
    const uid = parseInt(realUid);
    
    longIuNumbers.forEach((num, index) => {
        console.log(`\n数字 ${index + 1}: ${num}`);
        console.log(`与uid的差: ${num - uid}`);
        console.log(`与uid的比: ${(num / uid).toFixed(6)}`);
        console.log(`与upuid的差: ${num - contextData.upuid}`);
        console.log(`与upuid的比: ${(num / contextData.upuid).toFixed(6)}`);
        
        // 检查是否有简单的数学关系
        const operations = [
            { name: 'uid * 3', value: uid * 3 },
            { name: 'uid * 2.9', value: Math.floor(uid * 2.9) },
            { name: 'uid * 3.1', value: Math.floor(uid * 3.1) },
            { name: 'uid + upuid', value: uid + contextData.upuid },
            { name: 'uid + 5392000', value: uid + 5392000 },
            { name: 'upuid + 数字差', value: contextData.upuid + (num - contextData.upuid) }
        ];
        
        operations.forEach(op => {
            if (Math.abs(op.value - num) < 1000) {
                console.log(`  🔍 接近匹配: ${op.name} = ${op.value} (差${Math.abs(op.value - num)})`);
            }
        });
    });
}

// 基于uid尝试生成长格式iu
function tryUidBasedGeneration() {
    console.log("\n--- 基于uid的长格式iu生成尝试 ---");
    
    const uid = realUid;
    
    // 可能的转换方式
    const transformations = [
        // 直接使用uid
        uid,
        
        // uid的数学变换
        (parseInt(uid) * 3).toString(),
        (parseInt(uid) * 2).toString(),
        (parseInt(uid) + 5392000).toString(),
        (parseInt(uid) + contextData.upuid).toString(),
        
        // uid的字符串变换
        uid.split('').reverse().join(''),
        uid.repeat(2),
        uid + uid,
        
        // uid与上下文的组合
        uid + contextData.ch,
        contextData.ch + uid,
        uid + contextData.hqs,
        contextData.hqs + uid,
        uid + contextData.upuid,
        contextData.upuid + uid,
        
        // 复杂组合
        uid + '_' + contextData.ch + '_' + contextData.hqs,
        contextData.ch + '_' + uid + '_' + contextData.hqs,
        
        // 可能的编码
        parseInt(uid).toString(16), // 十六进制
        parseInt(uid).toString(36), // 36进制
        parseInt(uid).toString(8),  // 八进制
        parseInt(uid).toString(2),  // 二进制
    ];
    
    console.log("尝试基于uid的各种变换...");
    
    transformations.forEach((transform, transformIndex) => {
        console.log(`\n--- 变换 ${transformIndex + 1}: ${transform} ---`);
        
        // 可能的密钥
        const keys = [
            'pigxpigxpigxpigx',
            'iub0c4d3p2',
            'secret',
            'key',
            'salt',
            contextData.ch,
            contextData.hqs,
            contextData.ch + contextData.hqs
        ];
        
        let found = false;
        
        keys.forEach(key => {
            if (found) return;
            
            const inputs = [
                `${key}_${transform}`,
                `${transform}_${key}`,
                `${key}${transform}`,
                `${transform}${key}`,
                `${key}|${transform}`,
                `${transform}|${key}`
            ];
            
            inputs.forEach(input => {
                const md5Hash = crypto.createHash('md5').update(input).digest('hex');
                
                // 检查是否匹配任何一个目标哈希
                longIuHashes.forEach((targetHash, hashIndex) => {
                    if (md5Hash === targetHash) {
                        console.log(`🎯 找到匹配！`);
                        console.log(`  变换: ${transform}`);
                        console.log(`  密钥: ${key}`);
                        console.log(`  输入: ${input}`);
                        console.log(`  匹配的数字: ${longIuNumbers[hashIndex]}`);
                        console.log(`  MD5: ${md5Hash}`);
                        console.log(`  Base64: ${crypto.createHash('md5').update(input).digest('base64')}`);
                        found = true;
                    }
                });
            });
        });
        
        if (!found) {
            console.log(`❌ 变换${transformIndex + 1}未找到匹配`);
        }
    });
}

// 尝试基于时间戳的生成
function tryTimestampBasedGeneration() {
    console.log("\n--- 基于时间戳的生成尝试 ---");
    
    // 从API请求时间推算可能的时间戳
    const apiTime = "2025-08-28 12:41:06";
    const apiTimestamp = Math.floor(new Date(apiTime).getTime() / 1000);
    
    console.log(`API请求时间: ${apiTime}`);
    console.log(`API时间戳: ${apiTimestamp}`);
    
    // 生成可能的时间戳范围
    const timeVariants = [];
    
    // 在API时间前后生成时间戳
    for (let offset = -86400; offset <= 86400; offset += 3600) {
        timeVariants.push(apiTimestamp + offset);
    }
    
    // 添加一些特殊的时间戳
    timeVariants.push(
        Math.floor(apiTimestamp / 3600) * 3600, // 整点时间戳
        Math.floor(apiTimestamp / 86400) * 86400, // 整天时间戳
        1756356061, // 从API请求中看到的t参数
        1756362756, // 可能的相关时间戳
    );
    
    console.log(`生成了 ${timeVariants.length} 个时间戳变体`);
    
    let found = false;
    let attempts = 0;
    const maxAttempts = 500;
    
    for (const timestamp of timeVariants) {
        if (found || attempts >= maxAttempts) break;
        
        const timeInputs = [
            `${realUid}_${timestamp}`,
            `${timestamp}_${realUid}`,
            `uid_${realUid}_time_${timestamp}`,
            `time_${timestamp}_uid_${realUid}`,
            `${contextData.ch}_${realUid}_${timestamp}`,
            `${realUid}_${contextData.ch}_${timestamp}`,
            `pigx_${realUid}_${timestamp}`,
            `${realUid}_pigx_${timestamp}`
        ];
        
        timeInputs.forEach(input => {
            if (found || attempts >= maxAttempts) return;
            attempts++;
            
            const md5Hash = crypto.createHash('md5').update(input).digest('hex');
            
            longIuHashes.forEach((targetHash, hashIndex) => {
                if (md5Hash === targetHash) {
                    console.log(`🎯 时间戳匹配！`);
                    console.log(`  时间戳: ${timestamp} (${new Date(timestamp * 1000).toISOString()})`);
                    console.log(`  输入: ${input}`);
                    console.log(`  匹配的数字: ${longIuNumbers[hashIndex]}`);
                    console.log(`  MD5: ${md5Hash}`);
                    found = true;
                }
            });
        });
        
        if (attempts % 100 === 0) {
            console.log(`  已尝试 ${attempts} 种时间戳组合...`);
        }
    }
    
    console.log(`时间戳尝试完成，共 ${attempts} 次，${found ? '找到' : '未找到'}匹配`);
}

// 尝试基于特殊用户密钥的生成
function trySpecialUserKey() {
    console.log("\n--- 基于特殊用户密钥的生成 ---");
    
    // 从API请求中看到的special_user_key
    const specialUserKey = "rca68afddda2a897";
    console.log(`特殊用户密钥: ${specialUserKey}`);
    
    const inputs = [
        `${realUid}_${specialUserKey}`,
        `${specialUserKey}_${realUid}`,
        `${realUid}${specialUserKey}`,
        `${specialUserKey}${realUid}`,
        `${contextData.ch}_${realUid}_${specialUserKey}`,
        `${specialUserKey}_${realUid}_${contextData.ch}`,
        `${realUid}_${contextData.ch}_${specialUserKey}`,
        `user_${realUid}_key_${specialUserKey}`,
        `key_${specialUserKey}_user_${realUid}`
    ];
    
    let found = false;
    
    inputs.forEach((input, inputIndex) => {
        const md5Hash = crypto.createHash('md5').update(input).digest('hex');
        
        longIuHashes.forEach((targetHash, hashIndex) => {
            if (md5Hash === targetHash) {
                console.log(`🎯 特殊密钥匹配！`);
                console.log(`  输入: ${input}`);
                console.log(`  匹配的数字: ${longIuNumbers[hashIndex]}`);
                console.log(`  MD5: ${md5Hash}`);
                console.log(`  Base64: ${crypto.createHash('md5').update(input).digest('base64')}`);
                found = true;
            }
        });
    });
    
    if (!found) {
        console.log(`❌ 特殊用户密钥未找到匹配`);
    }
}

// 运行所有分析
analyzeNumberRelationships();
tryUidBasedGeneration();
tryTimestampBasedGeneration();
trySpecialUserKey();

console.log("\n=== 探索完成 ===");
console.log("基于真实API数据的新发现：");
console.log("• 真实uid: 2804420");
console.log("• 特殊用户密钥: rca68afddda2a897");
console.log("• API时间戳: 1756356061");
console.log("• 这些信息可能是长格式iu生成的关键");
