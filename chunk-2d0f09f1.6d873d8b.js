(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0f09f1"],{"9cc4":function(t,a,e){"use strict";e.r(a);var i=function(){var t=this,a=t._self._c;return a("div",{staticClass:"index__box content",staticStyle:{"padding-bottom":"5vw"}},[a("div",{staticClass:"block"},[a("div",{staticClass:"button",on:{click:t.go_task}},[t._v(" 阅读任务 ")])])])},n=[],s=e("dac6"),c=e("1ca4"),o=(e("d044"),e("c7d5"),{name:"Index",components:{},data(){return{init_data:{user:{id:0,money:0},read_account:{read_num:0},xcx_config:{appid:null,path:null},jump_read:!1},auto_go_read:!1,baseUrl:c["a"].baseUrl,is_show_qrcode:!1,is_make_qrcode:!1,read_url:"",clipboard:null,is_show_xcx_mask:!1,is_set_config:!1}},created(){let t=this.$route.query;var a=localStorage.getItem("iu");!t.code||a?this.init():s["a"].get(c["a"].baseUrl+"/task/login3?"+new URLSearchParams(t).toString()).then(t=>{0==t.data.code?(localStorage.setItem("iu",t.data.data.iu),this.init()):alert(t.msg)})},destroyed(){},mounted(){},methods:{init(){let t=c["a"].baseUrl+"/task";this.$route.query;s["a"].get(t).then(t=>{if(0==t.data.code){this.init_data=t.data.data;t.data.data.infoView;let a=this;this.init_data.xcx_config&&!a.is_set_config&&(a.is_set_config=!0,wx.config({debug:a.init_data.xcx_config.debug,appId:a.init_data.xcx_config.appId,timestamp:a.init_data.xcx_config.timestamp,nonceStr:a.init_data.xcx_config.nonceStr,signature:a.init_data.xcx_config.signature,jsApiList:["checkJsApi"],openTagList:["wx-open-launch-weapp"]}),wx.ready((function(){document.getElementById("launch-btn").addEventListener("launch",(function(t){a.show_xcx_mask()}))})))}})},go_task(){s["a"].get(c["a"].baseUrl+"/task/get_task_url").then(t=>{if(0==t.data.code){var a=t.data.data.url;location.href=a}else alert(t.data.msg)})}}}),d=o,r=e("2877"),l=Object(r["a"])(d,i,n,!1,null,null,null);a["default"]=l.exports}}]);