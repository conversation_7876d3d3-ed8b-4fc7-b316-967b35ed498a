{"app": "com.tencent.mm", "duration": "830ms", "headers": {"Host": "aappii.hufenaa.cn", "Connection": "keep-alive", "sec-ch-ua-platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 13; 22041211AC Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/138.0.7204.180 Mobile Safari/537.36 XWEB/1380123 MMWEBSDK/20250201 MMWEBID/6061 MicroMessenger/8.0.58.2841(0x28003A35) WeChat/arm64 Weixin NetType/4G Language/zh_CN ABI/arm64", "Accept": "*/*", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Android WebView\";v=\"138\"", "sec-ch-ua-mobile": "?1", "Origin": "http://qdld0828.gdhhaa3kej.xin", "X-Requested-With": "com.tencent.mm", "Sec-Fetch-Site": "cross-site", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://qdld0828.gdhhaa3kej.xin/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "method": "GET", "protocol": "HTTP/1.1", "remoteIp": "*************", "remotePort": 443, "sessionId": "5fb5c4a8-2f8c-4d56-a424-858f972108d2", "time": "2025-08-28 12:41:01", "url": "https://aappii.hufenaa.cn/user/login3?code=0415LKkl2pp3cg4xujll2GHwDc05LKkY&state=e7d9e254a7b6c3641cef18170efba77b"}