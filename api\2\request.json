{"app": "com.tencent.mm", "duration": "301ms", "headers": {"Host": "aappii.hufenaa.cn", "Connection": "keep-alive", "sec-ch-ua-platform": "\"Android\"", "User-Agent": "Mozilla/5.0 (Linux; Android 13; 22041211AC Build/TP1A.220624.014; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/138.0.7204.180 Mobile Safari/537.36 XWEB/1380123 MMWEBSDK/20250201 MMWEBID/6061 MicroMessenger/8.0.58.2841(0x28003A35) WeChat/arm64 Weixin NetType/4G Language/zh_CN ABI/arm64", "Accept": "*/*", "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Android WebView\";v=\"138\"", "sec-ch-ua-mobile": "?1", "Origin": "http://qdld0828.gdhhaa3kej.xin", "X-Requested-With": "com.tencent.mm", "Sec-Fetch-Site": "cross-site", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "http://qdld0828.gdhhaa3kej.xin/", "Accept-Encoding": "gzip, deflate, br, zstd", "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7"}, "method": "GET", "protocol": "HTTP/1.1", "remoteIp": "*************", "remotePort": 443, "sessionId": "a5183d19-d082-4da7-a0fb-b70b8ca616f6", "time": "2025-08-28 12:41:02", "url": "https://aappii.hufenaa.cn/read_channel2?iu=iuMjgwNDQyMA2&ch=bx65qqd&special_user_key=rca68afddda2a897"}