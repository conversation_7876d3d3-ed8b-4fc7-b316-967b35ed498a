/**
 * 最终解密方案 - 基于分析结果的推测
 */

const crypto = require('crypto');

// 原始数据
const testData = [
    { number: 8196230, encrypted: "iub0c4d3p2OTdhV3NDc205NGk5OW9oaEJ3SWRJZw2", decoded: "97aWsCsm94i99ohhBwIdIg" },
    { number: 8199733, encrypted: "iub0c4d3p2M3p3RVRFSElRV0JfTzNWMm1NM0RUTQ2", decoded: "3zwETEHIQWB_O3V2mM3DTM" },
    { number: 8201579, encrypted: "iub0c4d3p2OFo0OHljTmNsWllta1puWEN5UnNHRQ2", decoded: "8Z48ycNclZYmkZnXCyRsGE" }
];

const contextData = {
    ch: "bx65qqd",
    hqs: "fdj", 
    upuid: 8196926,
    xtp: "0kp"
};

console.log("=== 最终解密方案分析 ===");

// 基于观察到的模式，尝试构造加密函数
function generateEncryptedString(number, contextData) {
    const prefix = "iub0c4d3p2";
    const suffix = "2";
    
    // 尝试不同的组合方式生成中间部分
    const attempts = [
        // 方法1: 使用特定的盐值和算法
        () => {
            const input = `${number}_${contextData.ch}_salt`;
            const hash = crypto.createHash('md5').update(input).digest('base64');
            return hash.substring(0, 22); // 截取前22位
        },
        
        // 方法2: 使用时间戳相关的算法
        () => {
            const timestamp = Math.floor(Date.now() / 1000);
            const input = `${number}_${timestamp}`;
            const hash = crypto.createHash('sha1').update(input).digest('base64');
            return hash.substring(0, 22);
        },
        
        // 方法3: 使用HMAC算法
        () => {
            const key = contextData.ch + contextData.hqs;
            const hmac = crypto.createHmac('md5', key).update(number.toString()).digest('base64');
            return hmac.substring(0, 22);
        },
        
        // 方法4: 自定义算法 - 基于数字和上下文的组合
        () => {
            const seed = number + contextData.upuid;
            const input = `${seed}_${contextData.ch}`;
            const hash = crypto.createHash('sha256').update(input).digest('base64');
            return hash.substring(0, 22);
        },
        
        // 方法5: 可能是服务器端生成的随机字符串
        () => {
            // 这种情况下，中间部分可能是服务器根据某种算法生成的
            // 无法在客户端重现，需要通过API获取
            return "SERVER_GENERATED_HASH";
        }
    ];
    
    console.log(`\n--- 数字 ${number} 的加密尝试 ---`);
    
    attempts.forEach((attempt, index) => {
        try {
            const middle = attempt();
            const fullString = prefix + middle + suffix;
            console.log(`方法${index + 1}: ${fullString}`);
            
            // 检查是否与实际数据匹配
            const actualEncrypted = testData.find(d => d.number === number)?.encrypted;
            if (actualEncrypted && fullString === actualEncrypted) {
                console.log(`  ✓ 完全匹配！`);
                return fullString;
            }
        } catch (error) {
            console.log(`方法${index + 1}: 错误 - ${error.message}`);
        }
    });
    
    return null;
}

// 测试所有数据
testData.forEach(item => {
    generateEncryptedString(item.number, contextData);
});

// 基于分析结果的最可能的加密方法
function mostLikelyEncryptionMethod(number, contextData) {
    console.log("\n=== 最可能的加密方法 ===");
    
    // 观察：
    // 1. 所有字符串都有固定的前缀 "iub0c4d3p2" 和后缀 "2"
    // 2. 中间部分是22位Base64编码的字符串
    // 3. 解码后的内容看起来像是哈希值或随机字符串
    // 4. 长度固定为22位，可能是MD5哈希的Base64编码截取
    
    console.log("基于分析，这个加密方法很可能是：");
    console.log("1. 服务器端根据数字和某些参数生成一个哈希值");
    console.log("2. 将哈希值进行Base64编码");
    console.log("3. 可能截取特定长度（22位）");
    console.log("4. 添加固定的前缀和后缀");
    
    console.log("\n推荐的解决方案：");
    console.log("由于中间部分看起来是服务器生成的，建议：");
    console.log("1. 通过API调用获取完整的加密字符串");
    console.log("2. 或者找到服务器端的加密逻辑");
    
    return {
        prefix: "iub0c4d3p2",
        suffix: "2",
        middleLength: 22,
        encoding: "base64",
        note: "中间部分需要通过服务器API获取"
    };
}

// 提供一个模拟的加密函数（基于推测）
function simulateEncryption(number, contextData) {
    const prefix = "iub0c4d3p2";
    const suffix = "2";
    
    // 这里使用一个模拟的算法，实际算法可能不同
    const input = `${number}_${contextData.ch}_${contextData.upuid}`;
    const hash = crypto.createHash('md5').update(input).digest('base64');
    const middle = hash.substring(0, 22);
    
    return prefix + middle + suffix;
}

console.log("\n=== 模拟加密结果 ===");
testData.forEach(item => {
    const simulated = simulateEncryption(item.number, contextData);
    console.log(`数字 ${item.number}:`);
    console.log(`  实际: ${item.encrypted}`);
    console.log(`  模拟: ${simulated}`);
    console.log(`  匹配: ${simulated === item.encrypted ? '✓' : '✗'}`);
});

mostLikelyEncryptionMethod();

// 导出函数
module.exports = {
    simulateEncryption,
    mostLikelyEncryptionMethod
};
