/**
 * 最终服务端逻辑分析
 * 基于所有尝试的结果，提供最可能的服务端实现
 */

const crypto = require('crypto');

// 原始数据
const testData = [
    { 
        number: 8196230, 
        encrypted: "iub0c4d3p2OTdhV3NDc205NGk5OW9oaEJ3SWRJZw2",
        decoded: "97aWsCsm94i99ohhBwIdIg",
        hex: "f7b696b02b26f788bdf6886107021d22"
    },
    { 
        number: 8199733, 
        encrypted: "iub0c4d3p2M3p3RVRFSElRV0JfTzNWMm1NM0RUTQ2",
        decoded: "3zwETEHIQWB_O3V2mM3DTM",
        hex: "df3c044c41c841607f3b757698cdc34c"
    },
    { 
        number: 8201579, 
        encrypted: "iub0c4d3p2OFo0OHljTmNsWllta1puWEN5UnNHRQ2",
        decoded: "8Z48ycNclZYmkZnXCyRsGE",
        hex: "f19e3cc9c35c9596269199d70b246c18"
    }
];

const contextData = {
    ch: "bx65qqd",
    hqs: "fdj", 
    upuid: 8196926,
    xtp: "0kp"
};

console.log("=== 最终服务端逻辑分析 ===");

// 分析数字之间的关系
function analyzeNumberRelationships() {
    console.log("\n--- 数字关系分析 ---");
    
    const numbers = testData.map(d => d.number);
    console.log(`数字序列: ${numbers.join(', ')}`);
    
    // 计算差值
    const diff1 = numbers[1] - numbers[0]; // 8199733 - 8196230 = 3503
    const diff2 = numbers[2] - numbers[1]; // 8201579 - 8199733 = 1846
    
    console.log(`差值1: ${diff1}`);
    console.log(`差值2: ${diff2}`);
    console.log(`差值比: ${(diff1 / diff2).toFixed(2)}`);
    
    // 与upuid的关系
    numbers.forEach((num, i) => {
        const diff = num - contextData.upuid;
        console.log(`数字${i+1} - upuid = ${diff}`);
    });
    
    // 检查是否为递增序列
    const isIncreasing = numbers.every((num, i) => i === 0 || num > numbers[i-1]);
    console.log(`是否递增序列: ${isIncreasing}`);
}

// 分析MD5哈希的特征
function analyzeMD5Patterns() {
    console.log("\n--- MD5哈希特征分析 ---");
    
    testData.forEach((item, index) => {
        console.log(`\n数据组 ${index + 1}:`);
        console.log(`数字: ${item.number}`);
        console.log(`MD5: ${item.hex}`);
        
        // 分析哈希的特征
        const hex = item.hex;
        const bytes = [];
        for (let i = 0; i < hex.length; i += 2) {
            bytes.push(parseInt(hex.substr(i, 2), 16));
        }
        
        console.log(`字节值: ${bytes.join(', ')}`);
        console.log(`字节和: ${bytes.reduce((a, b) => a + b, 0)}`);
        console.log(`最大字节: ${Math.max(...bytes)}`);
        console.log(`最小字节: ${Math.min(...bytes)}`);
        
        // 检查是否有模式
        const firstByte = bytes[0];
        const lastByte = bytes[bytes.length - 1];
        console.log(`首字节: ${firstByte} (0x${firstByte.toString(16)})`);
        console.log(`末字节: ${lastByte} (0x${lastByte.toString(16)})`);
    });
}

// 尝试基于观察到的模式构造可能的服务端实现
function constructPossibleServerImplementation() {
    console.log("\n--- 可能的服务端实现构造 ---");
    
    console.log(`
基于所有分析，服务端的实现可能包含以下特征：

1. 确定的算法：MD5哈希（16字节输出）
2. 确定的编码：Base64编码（22位字符）
3. 确定的格式：iub0c4d3p2 + 22位Base64 + 2

未确定的部分：
1. 输入构造方式
2. 是否使用了服务端密钥
3. 是否包含了动态数据（时间戳、随机数等）
4. 是否有预处理步骤

最可能的实现方案：
`);

    // 方案1：包含服务端密钥的实现
    console.log("\n方案1：服务端密钥 + 数字");
    function serverImplementationV1(number, serverSecret) {
        const input = `${serverSecret}_${number}`;
        const md5Hash = crypto.createHash('md5').update(input).digest('base64');
        return `iub0c4d3p2${md5Hash}2`;
    }
    
    // 方案2：包含时间戳的实现
    console.log("\n方案2：数字 + 时间戳");
    function serverImplementationV2(number, timestamp) {
        const input = `${number}_${timestamp}`;
        const md5Hash = crypto.createHash('md5').update(input).digest('base64');
        return `iub0c4d3p2${md5Hash}2`;
    }
    
    // 方案3：复杂组合
    console.log("\n方案3：复杂组合");
    function serverImplementationV3(number, contextData, serverSecret, timestamp) {
        const input = `${serverSecret}_${number}_${contextData.ch}_${timestamp}`;
        const md5Hash = crypto.createHash('md5').update(input).digest('base64');
        return `iub0c4d3p2${md5Hash}2`;
    }
    
    console.log("示例调用：");
    console.log(`V1: ${serverImplementationV1(8196230, "unknown_server_secret")}`);
    console.log(`V2: ${serverImplementationV2(8196230, Math.floor(Date.now() / 1000))}`);
    console.log(`V3: ${serverImplementationV3(8196230, contextData, "secret", Math.floor(Date.now() / 1000))}`);
}

// 提供逆向工程的最终结论
function provideFinalConclusion() {
    console.log("\n=== 逆向工程最终结论 ===");
    
    console.log(`
经过详尽的分析和尝试，我们可以确定：

✅ 已确定的部分：
1. 加密字符串格式：iub0c4d3p2[22位Base64]2
2. 中间部分是MD5哈希的Base64编码（16字节 -> 22位Base64）
3. 每个数字对应唯一的MD5哈希值

❌ 无法确定的部分：
1. 具体的输入构造方式
2. 服务端使用的密钥或盐值
3. 是否包含动态数据（时间戳、随机数等）

🔍 可能的原因：
1. 服务端使用了我们无法获取的密钥
2. 包含了请求时的精确时间戳
3. 使用了其他上下文信息（IP地址、User-Agent等）
4. 有额外的预处理步骤

💡 实际应用建议：
由于无法完全复原服务端逻辑，建议：
1. 通过API调用获取'iu'值
2. 缓存获取到的'iu'值
3. 在请求中使用这个'iu'值

📝 服务端实现模板：
`);

    const serverTemplate = `
// 服务端可能的实现（伪代码）
function generateIu(number, contextData, request) {
    // 构造输入（具体方式未知）
    const input = constructSecretInput(number, contextData, request);
    
    // 计算MD5哈希
    const md5Hash = crypto.createHash('md5').update(input).digest('base64');
    
    // 返回完整格式
    return 'iub0c4d3p2' + md5Hash + '2';
}

function constructSecretInput(number, contextData, request) {
    // 可能包含：
    // - 服务端密钥
    // - 数字参数
    // - 上下文数据
    // - 时间戳
    // - 请求信息
    return /* 未知的构造逻辑 */;
}
`;
    
    console.log(serverTemplate);
}

// 运行所有分析
analyzeNumberRelationships();
analyzeMD5Patterns();
constructPossibleServerImplementation();
provideFinalConclusion();

// 导出分析结果
module.exports = {
    testData,
    contextData,
    conclusion: {
        format: "iub0c4d3p2[22位Base64]2",
        algorithm: "MD5哈希 + Base64编码",
        status: "部分逆向成功，输入构造方式未知",
        recommendation: "通过API获取，无法客户端生成"
    }
};
