/**
 * 十六进制模式分析 - 关键发现
 * 所有解码后的数据都是16字节，这很可能是MD5哈希！
 */

const crypto = require('crypto');

// 原始数据和十六进制
const testData = [
    { 
        number: 8196230, 
        decoded: "97aWsCsm94i99ohhBwIdIg",
        hex: "f7b696b02b26f788bdf6886107021d22"
    },
    { 
        number: 8199733, 
        decoded: "3zwETEHIQWB_O3V2mM3DTM",
        hex: "df3c044c41c841607f3b757698cdc34c"
    },
    { 
        number: 8201579, 
        decoded: "8Z48ycNclZYmkZnXCyRsGE",
        hex: "f19e3cc9c35c9596269199d70b246c18"
    }
];

const contextData = {
    ch: "bx65qqd",
    hqs: "fdj", 
    upuid: 8196926,
    xtp: "0kp"
};

console.log("=== 关键发现：16字节数据分析 ===");
console.log("所有解码后的数据都是16字节，这强烈暗示是MD5哈希！");

// 验证十六进制转换
testData.forEach((item, index) => {
    console.log(`\n数据组 ${index + 1}:`);
    console.log(`Base64: ${item.decoded}`);
    console.log(`十六进制: ${item.hex}`);
    
    // 验证转换是否正确
    const bufferFromBase64 = Buffer.from(item.decoded, 'base64');
    const hexFromBuffer = bufferFromBase64.toString('hex');
    console.log(`验证转换: ${hexFromBuffer}`);
    console.log(`转换正确: ${hexFromBuffer === item.hex}`);
});

// 现在尝试找出什么输入能产生这些MD5哈希
function findMD5Input() {
    console.log("\n=== 寻找MD5输入 ===");
    
    // 扩展可能的输入组合
    const generateInputs = (number) => {
        const inputs = [];
        
        // 基础组合
        inputs.push(number.toString());
        inputs.push(`${number}`);
        
        // 与上下文数据组合
        inputs.push(`${number}_${contextData.ch}`);
        inputs.push(`${contextData.ch}_${number}`);
        inputs.push(`${number}_${contextData.upuid}`);
        inputs.push(`${contextData.upuid}_${number}`);
        inputs.push(`${number}_${contextData.hqs}`);
        inputs.push(`${contextData.hqs}_${number}`);
        
        // 多参数组合
        inputs.push(`${number}_${contextData.ch}_${contextData.hqs}`);
        inputs.push(`${contextData.ch}_${number}_${contextData.hqs}`);
        inputs.push(`${contextData.ch}_${contextData.hqs}_${number}`);
        inputs.push(`${number}_${contextData.ch}_${contextData.upuid}`);
        inputs.push(`${contextData.upuid}_${number}_${contextData.ch}`);
        
        // 带前缀/后缀
        inputs.push(`iu_${number}`);
        inputs.push(`${number}_iu`);
        inputs.push(`task_${number}`);
        inputs.push(`${number}_task`);
        inputs.push(`user_${number}`);
        inputs.push(`${number}_user`);
        inputs.push(`salt_${number}`);
        inputs.push(`${number}_salt`);
        inputs.push(`key_${number}`);
        inputs.push(`${number}_key`);
        
        // 特殊格式
        inputs.push(`${number}|${contextData.ch}`);
        inputs.push(`${number}:${contextData.ch}`);
        inputs.push(`${number}&${contextData.ch}`);
        inputs.push(`${number}#${contextData.ch}`);
        
        // 数学运算结果
        inputs.push((number - contextData.upuid).toString());
        inputs.push((number + contextData.upuid).toString());
        inputs.push((number * 2).toString());
        inputs.push((number % 1000000).toString());
        
        // 时间相关
        const now = Math.floor(Date.now() / 1000);
        inputs.push(`${number}_${now}`);
        inputs.push(`${number}_${Math.floor(now / 3600)}`); // 小时
        inputs.push(`${number}_${Math.floor(now / 86400)}`); // 天
        
        return inputs;
    };
    
    testData.forEach((item, dataIndex) => {
        console.log(`\n=== 数据组 ${dataIndex + 1} (${item.number}) ===`);
        console.log(`目标MD5: ${item.hex}`);
        
        const inputs = generateInputs(item.number);
        let found = false;
        
        inputs.forEach((input, inputIndex) => {
            const md5Hash = crypto.createHash('md5').update(input).digest('hex');
            
            if (md5Hash === item.hex) {
                console.log(`🎯 找到匹配！`);
                console.log(`  输入: "${input}"`);
                console.log(`  MD5: ${md5Hash}`);
                console.log(`  Base64: ${crypto.createHash('md5').update(input).digest('base64')}`);
                found = true;
            }
        });
        
        if (!found) {
            console.log(`❌ 在${inputs.length}个输入中未找到匹配`);
            // 显示前几个尝试的结果作为参考
            console.log(`前5个尝试的结果:`);
            inputs.slice(0, 5).forEach((input, i) => {
                const md5Hash = crypto.createHash('md5').update(input).digest('hex');
                console.log(`  ${i+1}. "${input}" -> ${md5Hash}`);
            });
        }
    });
}

// 尝试更复杂的组合
function tryComplexCombinations() {
    console.log("\n=== 尝试复杂组合 ===");
    
    // 可能的分隔符
    const separators = ['_', '|', ':', '&', '#', '-', '+', '=', '/', '\\', '.', ','];
    
    // 可能的前缀/后缀
    const prefixes = ['', 'iu', 'task', 'user', 'auth', 'login', 'token', 'key', 'salt', 'hash'];
    const suffixes = ['', 'iu', 'task', 'user', 'auth', 'login', 'token', 'key', 'salt', 'hash'];
    
    testData.forEach((item, dataIndex) => {
        console.log(`\n--- 数据组 ${dataIndex + 1} 复杂组合测试 ---`);
        let attempts = 0;
        let found = false;
        
        // 限制尝试次数以避免过多输出
        const maxAttempts = 100;
        
        outerLoop: for (const prefix of prefixes.slice(0, 5)) {
            for (const suffix of suffixes.slice(0, 5)) {
                for (const sep of separators.slice(0, 3)) {
                    if (attempts >= maxAttempts) break outerLoop;
                    
                    // 构造输入
                    let input = '';
                    if (prefix) input += prefix + sep;
                    input += item.number;
                    if (suffix) input += sep + suffix;
                    
                    // 也尝试加入上下文数据
                    const inputs = [
                        input,
                        input + sep + contextData.ch,
                        contextData.ch + sep + input,
                        input + sep + contextData.upuid,
                        contextData.upuid + sep + input
                    ];
                    
                    for (const testInput of inputs) {
                        if (attempts >= maxAttempts) break outerLoop;
                        attempts++;
                        
                        const md5Hash = crypto.createHash('md5').update(testInput).digest('hex');
                        if (md5Hash === item.hex) {
                            console.log(`🎯 复杂组合匹配！`);
                            console.log(`  输入: "${testInput}"`);
                            console.log(`  MD5: ${md5Hash}`);
                            found = true;
                            break outerLoop;
                        }
                    }
                }
            }
        }
        
        console.log(`尝试了 ${attempts} 种组合，${found ? '找到' : '未找到'}匹配`);
    });
}

// 尝试包含特殊字符的输入
function trySpecialCharacters() {
    console.log("\n=== 尝试特殊字符输入 ===");
    
    // 可能包含的特殊字符或字符串
    const specialStrings = [
        'pigxpigxpigxpigx', // 从代码中看到的
        'server_secret',
        'encryption_key',
        'iu_generation',
        'task_auth',
        contextData.ch + contextData.hqs,
        contextData.ch + contextData.upuid,
        // 可能的时间戳格式
        '20241228', // 当前日期
        '2024-12-28',
        Math.floor(Date.now() / 1000).toString(),
        Math.floor(Date.now() / 1000 / 3600).toString() // 小时级时间戳
    ];
    
    testData.forEach((item, dataIndex) => {
        console.log(`\n--- 数据组 ${dataIndex + 1} 特殊字符测试 ---`);
        
        specialStrings.forEach((special, specialIndex) => {
            const inputs = [
                `${item.number}${special}`,
                `${special}${item.number}`,
                `${item.number}_${special}`,
                `${special}_${item.number}`,
                `${item.number}|${special}`,
                `${special}|${item.number}`
            ];
            
            inputs.forEach(input => {
                const md5Hash = crypto.createHash('md5').update(input).digest('hex');
                if (md5Hash === item.hex) {
                    console.log(`🎯 特殊字符匹配！`);
                    console.log(`  输入: "${input}"`);
                    console.log(`  特殊字符串: "${special}"`);
                    console.log(`  MD5: ${md5Hash}`);
                }
            });
        });
    });
}

// 运行所有分析
findMD5Input();
tryComplexCombinations();
trySpecialCharacters();

// 提供服务端实现建议
console.log("\n=== 服务端实现建议 ===");
console.log(`
基于16字节数据的发现，服务端的实现很可能是：

function generateIu(number, contextData) {
    // 1. 构造输入字符串（具体格式待确定）
    const input = constructInput(number, contextData);
    
    // 2. 计算MD5哈希
    const md5Hash = crypto.createHash('md5').update(input).digest();
    
    // 3. 转换为Base64（正好22位，因为16字节的Base64是22位）
    const base64Hash = md5Hash.toString('base64');
    
    // 4. 组装最终结果
    return 'iub0c4d3p2' + base64Hash + '2';
}

关键是找出 constructInput 函数的具体实现。
`);
