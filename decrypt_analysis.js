/**
 * 数字到字符串加密方法分析
 */

// 原始数据
const data = [
    { number: 8196230, encrypted: "iub0c4d3p2OTdhV3NDc205NGk5OW9oaEJ3SWRJZw2" },
    { number: 8199733, encrypted: "iub0c4d3p2M3p3RVRFSElRV0JfTzNWMm1NM0RUTQ2" },
    { number: 8201579, encrypted: "iub0c4d3p2OFo0OHljTmNsWllta1puWEN5UnNHRQ2" }
];

// 可能用到的数据
const contextData = {
    ch: "bx65qqd",
    hqs: "fdj", 
    upuid: 8196926,
    xtp: "0kp"
};

console.log("=== 加密字符串结构分析 ===");

data.forEach((item, index) => {
    console.log(`\n数据组 ${index + 1}:`);
    console.log(`数字: ${item.number}`);
    console.log(`加密字符串: ${item.encrypted}`);
    console.log(`长度: ${item.encrypted.length}`);
    
    // 分析字符串结构
    const prefix = item.encrypted.substring(0, 10); // "iub0c4d3p2"
    const middle = item.encrypted.substring(10, item.encrypted.length - 1);
    const suffix = item.encrypted.slice(-1); // "2"
    
    console.log(`前缀: ${prefix}`);
    console.log(`中间部分: ${middle}`);
    console.log(`后缀: ${suffix}`);
    
    // 尝试Base64解码中间部分
    try {
        const decoded = atob(middle);
        console.log(`Base64解码结果: ${decoded}`);
    } catch (e) {
        console.log(`Base64解码失败: ${e.message}`);
    }
});

console.log("\n=== 模式分析 ===");

// 分析共同特征
console.log("共同前缀: iub0c4d3p2");
console.log("共同后缀: 2");
console.log("中间部分都是Base64编码格式");

// 分析数字差异
console.log("\n数字差异分析:");
console.log(`8199733 - 8196230 = ${8199733 - 8196230}`);
console.log(`8201579 - 8199733 = ${8201579 - 8199733}`);
console.log(`upuid (8196926) 与第一个数字的差异: ${8196926 - 8196230}`);

// 尝试解密函数
function attemptDecryption(number, contextData) {
    console.log(`\n=== 尝试解密数字: ${number} ===`);
    
    // 方法1: 直接Base64编码数字
    const method1 = btoa(number.toString());
    console.log(`方法1 - 直接Base64编码: ${method1}`);
    
    // 方法2: 数字与upuid的关系
    const diff = number - contextData.upuid;
    const method2 = btoa(diff.toString());
    console.log(`方法2 - 与upuid差值Base64编码: ${method2}`);
    
    // 方法3: 结合其他参数
    const combined = `${number}_${contextData.ch}_${contextData.hqs}`;
    const method3 = btoa(combined);
    console.log(`方法3 - 组合参数Base64编码: ${method3}`);
    
    // 方法4: 时间戳相关
    const timestamp = Math.floor(number / 1000);
    const method4 = btoa(timestamp.toString());
    console.log(`方法4 - 时间戳Base64编码: ${method4}`);
    
    return {
        method1, method2, method3, method4
    };
}

// 测试解密
data.forEach(item => {
    attemptDecryption(item.number, contextData);
});

// 构造完整加密字符串的函数
function constructEncryptedString(number, contextData) {
    const prefix = "iub0c4d3p2";
    const suffix = "2";
    
    // 尝试不同的中间部分生成方法
    const methods = [
        btoa(number.toString()),
        btoa((number - contextData.upuid).toString()),
        btoa(`${number}_${contextData.ch}`),
        btoa(`${contextData.ch}_${number}`),
        btoa(`${number}${contextData.hqs}`)
    ];
    
    console.log(`\n=== 构造完整字符串 (数字: ${number}) ===`);
    methods.forEach((method, index) => {
        const fullString = prefix + method + suffix;
        console.log(`方法${index + 1}: ${fullString}`);
    });
}

// 测试构造
data.forEach(item => {
    constructEncryptedString(item.number, contextData);
});

// 反向工程 - 从已知加密字符串推导规律
console.log("\n=== 反向工程分析 ===");
data.forEach((item, index) => {
    const middle = item.encrypted.substring(10, item.encrypted.length - 1);
    try {
        const decoded = atob(middle);
        console.log(`\n数据组 ${index + 1}:`);
        console.log(`原数字: ${item.number}`);
        console.log(`Base64解码: ${decoded}`);
        
        // 检查是否包含原数字
        if (decoded.includes(item.number.toString())) {
            console.log("✓ 解码结果包含原数字");
        }
        
        // 检查是否包含上下文数据
        Object.entries(contextData).forEach(([key, value]) => {
            if (decoded.includes(value.toString())) {
                console.log(`✓ 解码结果包含 ${key}: ${value}`);
            }
        });
        
    } catch (e) {
        console.log(`解码失败: ${e.message}`);
    }
});
