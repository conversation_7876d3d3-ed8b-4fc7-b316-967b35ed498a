/**
 * localStorage中'iu'的生成方法 - 浏览器版本
 * 🎉 基于真实API数据成功复原的完整实现
 */

/**
 * iu生成函数 - 完全复原的服务端逻辑
 * 
 * 通过分析真实API请求发现的生成规律：
 * iu = "iu" + Base64(uid).replace(/=/g, '') + "2"
 * 
 * @param {string|number} uid - 用户ID
 * @returns {string} 生成的iu值
 */
function generateIu(uid) {
    // 1. 将uid转换为字符串
    const uidStr = uid.toString();
    
    // 2. Base64编码
    const base64Uid = btoa(uidStr);
    
    // 3. 去掉Base64填充字符（=）
    const withoutPadding = base64Uid.replace(/=/g, '');
    
    // 4. 组装最终结果：iu + Base64(uid) + 2
    return 'iu' + withoutPadding + '2';
}

/**
 * iu验证函数 - 验证iu值是否对应指定的uid
 * @param {string} iu - 要验证的iu值
 * @param {string|number} expectedUid - 期望的uid
 * @returns {boolean} 是否匹配
 */
function validateIu(iu, expectedUid) {
    try {
        // 检查格式
        if (!iu.startsWith('iu') || !iu.endsWith('2')) {
            return false;
        }
        
        // 提取中间部分
        const middle = iu.substring(2, iu.length - 1);
        
        // 添加可能需要的Base64填充
        const padded = middle + '='.repeat((4 - middle.length % 4) % 4);
        
        // Base64解码
        const decoded = atob(padded);
        
        // 比较
        return decoded === expectedUid.toString();
    } catch (e) {
        return false;
    }
}

/**
 * 从iu值中提取uid
 * @param {string} iu - iu值
 * @returns {string|null} 提取的uid，失败返回null
 */
function extractUidFromIu(iu) {
    try {
        if (!iu.startsWith('iu') || !iu.endsWith('2')) {
            return null;
        }
        
        const middle = iu.substring(2, iu.length - 1);
        const padded = middle + '='.repeat((4 - middle.length % 4) % 4);
        const decoded = atob(padded);
        
        return decoded;
    } catch (e) {
        return null;
    }
}

/**
 * 完整的localStorage iu管理类
 */
class IuManager {
    constructor() {
        this.storageKey = 'iu';
    }
    
    /**
     * 生成iu值
     * @param {string|number} uid - 用户ID
     * @returns {string} iu值
     */
    generate(uid) {
        return generateIu(uid);
    }
    
    /**
     * 存储iu到localStorage
     * @param {string} iu - iu值
     */
    store(iu) {
        if (typeof localStorage !== 'undefined') {
            localStorage.setItem(this.storageKey, iu);
        }
    }
    
    /**
     * 从localStorage获取iu
     * @returns {string|null} iu值
     */
    get() {
        if (typeof localStorage !== 'undefined') {
            return localStorage.getItem(this.storageKey);
        }
        return null;
    }
    
    /**
     * 验证当前存储的iu是否对应指定uid
     * @param {string|number} uid - 用户ID
     * @returns {boolean} 是否匹配
     */
    validate(uid) {
        const iu = this.get();
        return iu ? validateIu(iu, uid) : false;
    }
    
    /**
     * 生成并存储iu
     * @param {string|number} uid - 用户ID
     * @returns {string} 生成的iu值
     */
    generateAndStore(uid) {
        const iu = this.generate(uid);
        this.store(iu);
        return iu;
    }
    
    /**
     * 清除存储的iu
     */
    clear() {
        if (typeof localStorage !== 'undefined') {
            localStorage.removeItem(this.storageKey);
        }
    }
    
    /**
     * 检查是否需要更新iu（当uid变化时）
     * @param {string|number} currentUid - 当前用户ID
     * @returns {boolean} 是否需要更新
     */
    needsUpdate(currentUid) {
        const storedIu = this.get();
        if (!storedIu) return true;
        
        const extractedUid = extractUidFromIu(storedIu);
        return extractedUid !== currentUid.toString();
    }
    
    /**
     * 确保iu与当前uid匹配，不匹配则重新生成
     * @param {string|number} uid - 用户ID
     * @returns {string} iu值
     */
    ensureValid(uid) {
        if (this.needsUpdate(uid)) {
            return this.generateAndStore(uid);
        }
        return this.get();
    }
}

/**
 * 在HTTP请求中使用iu的示例
 */
function addIuToRequest(requestConfig, uid) {
    const iuManager = new IuManager();
    const iu = iuManager.ensureValid(uid);
    
    // 添加iu参数到请求
    if (requestConfig.params) {
        requestConfig.params.iu = iu;
    } else {
        requestConfig.params = { iu: iu };
    }
    
    return requestConfig;
}

/**
 * 完整的使用示例
 */
function exampleUsage() {
    console.log("=== iu生成和使用示例 ===");
    
    // 创建管理器
    const iuManager = new IuManager();
    
    // 假设从API获取到的用户ID
    const userId = "2804420";
    
    // 生成并存储iu
    const iu = iuManager.generateAndStore(userId);
    console.log(`用户ID: ${userId}`);
    console.log(`生成的iu: ${iu}`);
    
    // 验证
    const isValid = iuManager.validate(userId);
    console.log(`验证结果: ${isValid ? '有效' : '无效'}`);
    
    // 在请求中使用
    const requestConfig = {
        url: '/api/some-endpoint',
        method: 'GET',
        params: {
            ch: 'bx65qqd',
            other: 'params'
        }
    };
    
    const configWithIu = addIuToRequest(requestConfig, userId);
    console.log('请求配置:', configWithIu);
    
    return iu;
}

// 如果在浏览器环境中，可以直接测试
if (typeof window !== 'undefined') {
    // 浏览器环境
    window.generateIu = generateIu;
    window.validateIu = validateIu;
    window.extractUidFromIu = extractUidFromIu;
    window.IuManager = IuManager;
    
    console.log("iu生成函数已加载到全局作用域");
    console.log("使用方法：");
    console.log("1. generateIu('2804420') - 生成iu");
    console.log("2. new IuManager() - 创建管理器");
    console.log("3. validateIu(iu, uid) - 验证iu");
}

// Node.js环境导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        generateIu,
        validateIu,
        extractUidFromIu,
        IuManager,
        addIuToRequest,
        exampleUsage
    };
}

// 测试数据验证（仅在控制台输出）
console.log("=== 🎉 iu生成逻辑复原成功！ ===");
console.log("");
console.log("📋 复原结果总结：");
console.log("• 格式：iu + Base64(uid).replace(/=/g, '') + 2");
console.log("• 示例：uid '2804420' -> iu 'iuMjgwNDQyMA2'");
console.log("• 验证：✅ 与真实API数据完全匹配");
console.log("");
console.log("🔧 使用方法：");
console.log("1. const iu = generateIu('your_uid');");
console.log("2. localStorage.setItem('iu', iu);");
console.log("3. 在请求中使用这个iu值");
console.log("");
console.log("🎯 关键发现：");
console.log("• 真实API使用简单的Base64编码uid");
console.log("• 之前分析的复杂格式是另一种系统");
console.log("• 可以在客户端直接生成，无需服务端API");

// 快速测试
const testUid = "2804420";
const testIu = generateIu(testUid);
const isTestValid = validateIu(testIu, testUid);
const extractedUid = extractUidFromIu(testIu);

console.log("");
console.log("🧪 快速验证：");
console.log(`输入uid: ${testUid}`);
console.log(`生成iu:  ${testIu}`);
console.log(`验证:    ${isTestValid ? '✅ 通过' : '❌ 失败'}`);
console.log(`提取uid: ${extractedUid}`);
console.log(`匹配:    ${extractedUid === testUid ? '✅ 完全匹配' : '❌ 不匹配'}`);
