/**
 * localStorage中'iu'的生成和使用方法
 * 基于代码分析提炼的完整逻辑
 */

// 1. 检查localStorage中是否已存在'iu'
function getIuFromStorage() {
    return localStorage.getItem("iu")
}

// 2. 'iu'的生成逻辑（通过API获取）
function generateIu(queryParams) {
    const baseUrl = getBaseUrl()
    const url = `${baseUrl}/task/login3?${new URLSearchParams(queryParams).toString()}`

    return fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data.code === 0) {
                const iu = data.data.iu
                localStorage.setItem("iu", iu)
                return iu
            } else {
                throw new Error(data.msg || '获取iu失败')
            }
        })
}

// 3. 获取baseUrl的方法
function getBaseUrl() {
    const hostname = location.href

    // 检查是否为云服务域名
    if (hostname.indexOf("myqcloud.com") > -1 ||
        hostname.indexOf("ctyun.cn") > -1 ||
        hostname.indexOf("cmecloud.cn") > -1 ||
        hostname.indexOf("ksyuncs.com") > -1) {
        return "https://m.chchapi.cn"
    } else {
        return "http://" + location.host
    }
}

// 4. 在HTTP请求拦截器中使用'iu'
function addIuToRequest(requestConfig) {
    let iu = localStorage.getItem("iu")
    if (iu) {
        requestConfig.params = {
            iu: iu,
            ...requestConfig.params
        }
    }
    return requestConfig
}

// 5. 完整的初始化流程
function initializeIu(routeQuery) {
    return new Promise((resolve, reject) => {
        // 首先检查localStorage中是否已有iu
        const existingIu = getIuFromStorage()

        // 如果URL中没有code参数，或者已经有iu，直接初始化
        if (!routeQuery.code || existingIu) {
            resolve(existingIu)
            return
        }

        // 否则通过API获取新的iu
        generateIu(routeQuery)
            .then(iu => {
                resolve(iu)
            })
            .catch(error => {
                alert(error.message)
                reject(error)
            })
    })
}

// 6. 使用示例
function exampleUsage() {
    // 在Vue组件的created钩子中使用
    const routeQuery = this.$route.query // Vue路由查询参数

    initializeIu(routeQuery)
        .then(iu => {
            console.log('iu已准备就绪:', iu)
            // 继续执行其他初始化逻辑
            this.init()
        })
        .catch(error => {
            console.error('iu初始化失败:', error)
        })
}

// 导出主要方法
export {
    getIuFromStorage,
    generateIu,
    getBaseUrl,
    addIuToRequest,
    initializeIu
}
