/**
 * 哈希算法分析 - 尝试MD5等常见哈希算法
 */

const crypto = require('crypto');

// 原始数据
const data = [
    { number: 8196230, encrypted: "iub0c4d3p2OTdhV3NDc205NGk5OW9oaEJ3SWRJZw2", decoded: "97aWsCsm94i99ohhBwIdIg" },
    { number: 8199733, encrypted: "iub0c4d3p2M3p3RVRFSElRV0JfTzNWMm1NM0RUTQ2", decoded: "3zwETEHIQWB_O3V2mM3DTM" },
    { number: 8201579, encrypted: "iub0c4d3p2OFo0OHljTmNsWllta1puWEN5UnNHRQ2", decoded: "8Z48ycNclZYmkZnXCyRsGE" }
];

const contextData = {
    ch: "bx65qqd",
    hqs: "fdj", 
    upuid: 8196926,
    xtp: "0kp"
};

console.log("=== 哈希算法分析 ===");

// 尝试不同的哈希算法和输入组合
function tryHashAlgorithms(input, label) {
    console.log(`\n--- ${label}: "${input}" ---`);
    
    const algorithms = ['md5', 'sha1', 'sha256'];
    
    algorithms.forEach(algo => {
        const hash = crypto.createHash(algo).update(input.toString()).digest('hex');
        const hashBase64 = crypto.createHash(algo).update(input.toString()).digest('base64');
        
        console.log(`${algo.toUpperCase()}: ${hash}`);
        console.log(`${algo.toUpperCase()} (Base64): ${hashBase64}`);
        
        // 检查是否匹配解码结果
        data.forEach((item, index) => {
            if (hashBase64.includes(item.decoded) || item.decoded.includes(hashBase64)) {
                console.log(`  ✓ 可能匹配数据组 ${index + 1}`);
            }
            // 检查部分匹配
            if (hashBase64.substring(0, 10) === item.decoded.substring(0, 10)) {
                console.log(`  ✓ 前10位匹配数据组 ${index + 1}`);
            }
        });
    });
}

// 测试不同的输入组合
data.forEach((item, index) => {
    console.log(`\n=== 数据组 ${index + 1} 分析 ===`);
    console.log(`目标解码结果: ${item.decoded}`);
    
    // 尝试各种输入组合
    const inputs = [
        item.number,
        `${item.number}`,
        `${item.number}_${contextData.ch}`,
        `${contextData.ch}_${item.number}`,
        `${item.number}_${contextData.hqs}`,
        `${item.number}_${contextData.upuid}`,
        `${contextData.ch}${item.number}${contextData.hqs}`,
        `${item.number}${contextData.ch}${contextData.hqs}`,
        `${contextData.upuid}_${item.number}_${contextData.ch}`,
        item.number - contextData.upuid,
        `salt_${item.number}`,
        `${item.number}_salt`,
        `key_${item.number}_${contextData.ch}`
    ];
    
    inputs.forEach((input, inputIndex) => {
        tryHashAlgorithms(input, `输入${inputIndex + 1}`);
    });
});

// 尝试特定的编码方式
console.log("\n=== 特殊编码分析 ===");

function trySpecialEncoding(number, contextData) {
    console.log(`\n--- 数字: ${number} ---`);
    
    // 方法1: 可能是某种自定义base64变体
    const customBase64Chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
    
    // 方法2: 可能使用了密钥
    const possibleKeys = [contextData.ch, contextData.hqs, contextData.upuid.toString(), "pigxpigxpigxpigx"];
    
    possibleKeys.forEach(key => {
        // 尝试HMAC
        const hmacMd5 = crypto.createHmac('md5', key).update(number.toString()).digest('base64');
        const hmacSha1 = crypto.createHmac('sha1', key).update(number.toString()).digest('base64');
        
        console.log(`HMAC-MD5 (key: ${key}): ${hmacMd5}`);
        console.log(`HMAC-SHA1 (key: ${key}): ${hmacSha1}`);
    });
    
    // 方法3: 可能是AES加密
    possibleKeys.forEach(key => {
        try {
            if (key.length >= 16) {
                const cipher = crypto.createCipher('aes-128-cbc', key);
                let encrypted = cipher.update(number.toString(), 'utf8', 'base64');
                encrypted += cipher.final('base64');
                console.log(`AES-128-CBC (key: ${key}): ${encrypted}`);
            }
        } catch (e) {
            // 忽略加密错误
        }
    });
}

data.forEach(item => {
    trySpecialEncoding(item.number, contextData);
});

// 分析解码结果的特征
console.log("\n=== 解码结果特征分析 ===");
data.forEach((item, index) => {
    console.log(`\n数据组 ${index + 1}: ${item.decoded}`);
    console.log(`长度: ${item.decoded.length}`);
    console.log(`字符集: ${[...new Set(item.decoded)].join('')}`);
    
    // 检查是否符合某种编码格式
    const hasNumbers = /\d/.test(item.decoded);
    const hasLowerCase = /[a-z]/.test(item.decoded);
    const hasUpperCase = /[A-Z]/.test(item.decoded);
    const hasSpecialChars = /[^a-zA-Z0-9]/.test(item.decoded);
    
    console.log(`包含数字: ${hasNumbers}`);
    console.log(`包含小写字母: ${hasLowerCase}`);
    console.log(`包含大写字母: ${hasUpperCase}`);
    console.log(`包含特殊字符: ${hasSpecialChars}`);
});
