/**
 * 专注于长格式iu的分析
 * 基于具体的例子：iub0c4d3p2M3p3RVRFSElRV0JfTzNWMm1NM0RUTQ2
 */

const crypto = require('crypto');

// 你提供的具体例子
const targetIu = "iub0c4d3p2M3p3RVRFSElRV0JfTzNWMm1NM0RUTQ2";
const correspondingNumber = 8199733; // 从之前的数据推测

console.log("=== 专注分析长格式iu ===");
console.log(`目标iu: ${targetIu}`);
console.log(`对应数字: ${correspondingNumber}`);

// 解析目标iu
function analyzeTargetIu() {
    console.log("\n--- 目标iu结构分析 ---");
    
    const prefix = targetIu.substring(0, 10); // "iub0c4d3p2"
    const middle = targetIu.substring(10, targetIu.length - 1); // "M3p3RVRFSElRV0JfTzNWMm1NM0RUTQ"
    const suffix = targetIu.slice(-1); // "2"
    
    console.log(`前缀: ${prefix}`);
    console.log(`中间: ${middle}`);
    console.log(`后缀: ${suffix}`);
    console.log(`中间长度: ${middle.length}`);
    
    // Base64解码中间部分
    try {
        const decoded = atob(middle);
        console.log(`Base64解码: ${decoded}`);
        console.log(`解码长度: ${decoded.length}`);
        
        // 转换为十六进制
        const hex = Buffer.from(decoded, 'binary').toString('hex');
        console.log(`十六进制: ${hex}`);
        console.log(`十六进制长度: ${hex.length}`);
        
        if (hex.length === 32) {
            console.log("✅ 确认是16字节数据，很可能是MD5哈希");
            return { decoded, hex, isValidMD5: true };
        } else {
            console.log(`❌ 不是16字节数据，长度为${hex.length/2}字节`);
            return { decoded, hex, isValidMD5: false };
        }
        
    } catch (e) {
        console.log(`Base64解码失败: ${e.message}`);
        return null;
    }
}

// 基于具体数字进行更精确的尝试
function trySpecificNumberPatterns() {
    console.log("\n--- 基于具体数字的精确尝试 ---");
    
    const number = correspondingNumber;
    const targetHex = "df3c044c41c841607f3b757698cdc34c"; // 从之前分析得出
    
    console.log(`目标数字: ${number}`);
    console.log(`目标MD5: ${targetHex}`);
    
    // 更广泛的密钥尝试
    const extendedSecrets = [
        // 基础密钥
        'secret', 'key', 'salt', 'password', 'token', 'auth', 'login', 'user', 'task', 'iu',
        
        // 数字密钥
        '123456', '654321', '888888', '666666', '999999', '111111', '000000',
        '1234567890', '0987654321', '1111111111', '2222222222',
        
        // 字母密钥
        'abcdefg', 'qwerty', 'admin', 'root', 'test', 'demo', 'sample',
        
        // 混合密钥
        'key123', 'salt456', 'secret789', 'admin123', 'test123',
        
        // 可能的系统密钥
        'system_key', 'app_secret', 'api_key', 'hash_salt', 'encrypt_key',
        'server_secret', 'private_key', 'master_key', 'crypto_salt',
        
        // 基于前缀的密钥
        'iub0c4d3p2', 'b0c4d3p2', '0c4d3p2', 'c4d3p2', '4d3p2', 'd3p2', '3p2', 'p2',
        
        // 可能的时间相关密钥
        '2024', '2023', '2025', '20240101', '20231228', '20241228',
        
        // 可能的版本密钥
        'v1', 'v2', 'v3', 'version1', 'version2', 'ver1', 'ver2',
        
        // 可能的应用相关密钥
        'app', 'web', 'mobile', 'client', 'server', 'api', 'service',
        
        // 可能的中文拼音密钥
        'yonghu', 'renwu', 'denglu', 'miyao', 'jiami', 'anquan',
        
        // 可能的特殊字符密钥
        '!@#$%^&*()', '123!@#', 'abc!@#', 'key!@#', 'salt!@#'
    ];
    
    console.log(`尝试 ${extendedSecrets.length} 个密钥...`);
    
    let found = false;
    let attempts = 0;
    
    for (const secret of extendedSecrets) {
        if (found) break;
        
        // 更多的输入格式
        const inputFormats = [
            // 基础格式
            `${secret}_${number}`,
            `${number}_${secret}`,
            `${secret}${number}`,
            `${number}${secret}`,
            
            // 带分隔符
            `${secret}|${number}`,
            `${number}|${secret}`,
            `${secret}:${number}`,
            `${number}:${secret}`,
            `${secret}#${number}`,
            `${number}#${secret}`,
            `${secret}&${number}`,
            `${number}&${secret}`,
            `${secret}=${number}`,
            `${number}=${secret}`,
            `${secret}@${number}`,
            `${number}@${secret}`,
            `${secret}.${number}`,
            `${number}.${secret}`,
            `${secret}-${number}`,
            `${number}-${secret}`,
            `${secret}+${number}`,
            `${number}+${secret}`,
            `${secret}/${number}`,
            `${number}/${secret}`,
            `${secret}\\${number}`,
            `${number}\\${secret}`,
            
            // 带前后缀
            `[${secret}]${number}`,
            `${number}[${secret}]`,
            `(${secret})${number}`,
            `${number}(${secret})`,
            `{${secret}}${number}`,
            `${number}{${secret}}`,
            `<${secret}>${number}`,
            `${number}<${secret}>`,
            
            // 重复模式
            `${secret}${secret}${number}`,
            `${number}${secret}${secret}`,
            `${secret}_${secret}_${number}`,
            `${number}_${secret}_${secret}`,
            
            // 大小写变体
            `${secret.toUpperCase()}_${number}`,
            `${number}_${secret.toUpperCase()}`,
            `${secret.toLowerCase()}_${number}`,
            `${number}_${secret.toLowerCase()}`,
            
            // 反转
            `${secret.split('').reverse().join('')}_${number}`,
            `${number}_${secret.split('').reverse().join('')}`
        ];
        
        for (const input of inputFormats) {
            attempts++;
            
            try {
                const md5Hash = crypto.createHash('md5').update(input).digest('hex');
                
                if (md5Hash === targetHex) {
                    console.log(`🎯 找到匹配！`);
                    console.log(`  密钥: "${secret}"`);
                    console.log(`  输入格式: "${input}"`);
                    console.log(`  MD5: ${md5Hash}`);
                    console.log(`  Base64: ${crypto.createHash('md5').update(input).digest('base64')}`);
                    
                    // 验证完整的iu生成
                    const base64Hash = crypto.createHash('md5').update(input).digest('base64');
                    const generatedIu = `iub0c4d3p2${base64Hash}2`;
                    console.log(`  生成的完整iu: ${generatedIu}`);
                    console.log(`  与目标iu匹配: ${generatedIu === targetIu ? '✅' : '❌'}`);
                    
                    found = true;
                    return { secret, input, md5Hash, base64Hash, generatedIu };
                }
            } catch (e) {
                // 忽略错误，继续尝试
            }
            
            // 每1000次尝试显示进度
            if (attempts % 1000 === 0) {
                console.log(`  已尝试 ${attempts} 种组合...`);
            }
        }
    }
    
    console.log(`总共尝试了 ${attempts} 种组合，${found ? '找到' : '未找到'}匹配`);
    return null;
}

// 尝试HMAC算法
function tryHMACAlgorithms() {
    console.log("\n--- HMAC算法尝试 ---");
    
    const number = correspondingNumber;
    const targetHex = "df3c044c41c841607f3b757698cdc34c";
    
    const hmacKeys = [
        'pigxpigxpigxpigx', 'secret', 'key', 'salt', 'iub0c4d3p2',
        '123456', 'admin', 'password', 'token', 'auth'
    ];
    
    const hmacInputs = [
        number.toString(),
        `${number}`,
        `user_${number}`,
        `task_${number}`,
        `id_${number}`,
        `${number}_data`,
        `${number}_hash`
    ];
    
    let found = false;
    
    for (const key of hmacKeys) {
        if (found) break;
        
        for (const input of hmacInputs) {
            // 尝试不同的HMAC算法
            const algorithms = ['md5', 'sha1', 'sha256'];
            
            for (const algo of algorithms) {
                try {
                    const hmacHash = crypto.createHmac(algo, key).update(input).digest('hex');
                    
                    if (hmacHash === targetHex) {
                        console.log(`🎯 HMAC匹配！`);
                        console.log(`  算法: HMAC-${algo.toUpperCase()}`);
                        console.log(`  密钥: "${key}"`);
                        console.log(`  输入: "${input}"`);
                        console.log(`  哈希: ${hmacHash}`);
                        found = true;
                        break;
                    }
                } catch (e) {
                    // 忽略错误
                }
            }
            
            if (found) break;
        }
    }
    
    if (!found) {
        console.log(`❌ HMAC算法未找到匹配`);
    }
}

// 尝试其他哈希算法
function tryOtherHashAlgorithms() {
    console.log("\n--- 其他哈希算法尝试 ---");
    
    const number = correspondingNumber;
    const targetHex = "df3c044c41c841607f3b757698cdc34c";
    
    const inputs = [
        number.toString(),
        `salt_${number}`,
        `${number}_salt`,
        `key_${number}`,
        `${number}_key`
    ];
    
    const algorithms = ['sha1', 'sha256', 'sha512'];
    
    let found = false;
    
    for (const input of inputs) {
        if (found) break;
        
        for (const algo of algorithms) {
            try {
                const hash = crypto.createHash(algo).update(input).digest('hex');
                
                // 检查是否前16字节匹配
                if (hash.substring(0, 32) === targetHex) {
                    console.log(`🎯 ${algo.toUpperCase()}前16字节匹配！`);
                    console.log(`  输入: "${input}"`);
                    console.log(`  完整哈希: ${hash}`);
                    console.log(`  前16字节: ${hash.substring(0, 32)}`);
                    found = true;
                    break;
                }
            } catch (e) {
                // 忽略错误
            }
        }
    }
    
    if (!found) {
        console.log(`❌ 其他哈希算法未找到匹配`);
    }
}

// 运行分析
const analysis = analyzeTargetIu();

if (analysis && analysis.isValidMD5) {
    console.log("\n确认是MD5哈希，开始尝试复原输入...");
    
    const result = trySpecificNumberPatterns();
    
    if (!result) {
        tryHMACAlgorithms();
        tryOtherHashAlgorithms();
    }
} else {
    console.log("\n无法确认哈希类型，跳过复原尝试");
}

console.log("\n=== 分析完成 ===");
console.log("如果没有找到匹配，可能的原因：");
console.log("1. 使用了我们没有尝试的密钥");
console.log("2. 输入格式更复杂");
console.log("3. 包含了动态数据（时间戳、随机数等）");
console.log("4. 使用了自定义的哈希算法或多重哈希");
console.log("5. 需要更多的上下文信息或真实数据样本");
