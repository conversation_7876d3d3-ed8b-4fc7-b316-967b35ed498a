/**
 * 服务端生成逻辑复原尝试
 * 基于3组数据进行逆向工程
 */

const crypto = require('crypto');

// 原始数据
const testData = [
    { number: 8196230, encrypted: "iub0c4d3p2OTdhV3NDc205NGk5OW9oaEJ3SWRJZw2", decoded: "97aWsCsm94i99ohhBwIdIg" },
    { number: 8199733, encrypted: "iub0c4d3p2M3p3RVRFSElRV0JfTzNWMm1NM0RUTQ2", decoded: "3zwETEHIQWB_O3V2mM3DTM" },
    { number: 8201579, encrypted: "iub0c4d3p2OFo0OHljTmNsWllta1puWEN5UnNHRQ2", decoded: "8Z48ycNclZYmkZnXCyRsGE" }
];

const contextData = {
    ch: "bx65qqd",
    hqs: "fdj", 
    upuid: 8196926,
    xtp: "0kp"
};

console.log("=== 服务端逻辑逆向工程 ===");

// 分析解码后的字符串特征
function analyzeDecodedStrings() {
    console.log("\n--- 解码字符串特征分析 ---");
    
    testData.forEach((item, index) => {
        const decoded = item.decoded;
        console.log(`\n数据组 ${index + 1}: ${decoded}`);
        console.log(`长度: ${decoded.length}`);
        
        // 转换为十六进制查看
        const hex = Buffer.from(decoded, 'base64').toString('hex');
        console.log(`十六进制: ${hex}`);
        console.log(`字节长度: ${hex.length / 2}`);
        
        // 分析字符分布
        const chars = [...decoded];
        const charCount = {};
        chars.forEach(char => {
            charCount[char] = (charCount[char] || 0) + 1;
        });
        console.log(`字符分布:`, charCount);
    });
}

// 尝试各种可能的服务端算法
function tryServerAlgorithms() {
    console.log("\n--- 尝试复原服务端算法 ---");
    
    // 可能的密钥组合
    const possibleKeys = [
        "pigxpigxpigxpigx", // 从之前代码中看到的密钥
        contextData.ch,
        contextData.hqs,
        contextData.ch + contextData.hqs,
        contextData.upuid.toString(),
        "server_secret_key",
        "iu_generation_key",
        "task_encryption_key"
    ];
    
    // 可能的输入组合
    const getInputCombinations = (number) => [
        number.toString(),
        `${number}_${contextData.ch}`,
        `${number}_${contextData.upuid}`,
        `${contextData.ch}_${number}`,
        `${number}_${contextData.ch}_${contextData.hqs}`,
        `${contextData.upuid}_${number}`,
        `task_${number}`,
        `iu_${number}`,
        `${number}_salt`,
        `salt_${number}`,
        `${number}_${Date.now()}`, // 时间戳相关
        `${number}_${Math.floor(Date.now() / 1000)}` // Unix时间戳
    ];
    
    testData.forEach((item, dataIndex) => {
        console.log(`\n=== 数据组 ${dataIndex + 1} (${item.number}) ===`);
        console.log(`目标: ${item.decoded}`);
        
        const inputs = getInputCombinations(item.number);
        
        inputs.forEach((input, inputIndex) => {
            possibleKeys.forEach((key, keyIndex) => {
                // 尝试不同的哈希算法
                const algorithms = [
                    { name: 'MD5', func: (data) => crypto.createHash('md5').update(data).digest('base64') },
                    { name: 'SHA1', func: (data) => crypto.createHash('sha1').update(data).digest('base64') },
                    { name: 'SHA256', func: (data) => crypto.createHash('sha256').update(data).digest('base64') },
                    { name: 'HMAC-MD5', func: (data) => crypto.createHmac('md5', key).update(data).digest('base64') },
                    { name: 'HMAC-SHA1', func: (data) => crypto.createHmac('sha1', key).update(data).digest('base64') },
                    { name: 'HMAC-SHA256', func: (data) => crypto.createHmac('sha256', key).update(data).digest('base64') }
                ];
                
                algorithms.forEach(algo => {
                    try {
                        const result = algo.func(input);
                        
                        // 检查完全匹配
                        if (result === item.decoded) {
                            console.log(`🎯 完全匹配！`);
                            console.log(`  算法: ${algo.name}`);
                            console.log(`  输入: ${input}`);
                            console.log(`  密钥: ${key}`);
                            console.log(`  结果: ${result}`);
                        }
                        
                        // 检查前缀匹配
                        if (result.substring(0, 10) === item.decoded.substring(0, 10)) {
                            console.log(`🔍 前10位匹配:`);
                            console.log(`  算法: ${algo.name}, 输入: ${input}, 密钥: ${key}`);
                        }
                        
                        // 检查截取匹配（可能服务端截取了哈希的一部分）
                        if (result.substring(0, 22) === item.decoded) {
                            console.log(`✂️ 截取22位匹配！`);
                            console.log(`  算法: ${algo.name}`);
                            console.log(`  输入: ${input}`);
                            console.log(`  密钥: ${key}`);
                            console.log(`  完整结果: ${result}`);
                            console.log(`  截取结果: ${result.substring(0, 22)}`);
                        }
                        
                    } catch (error) {
                        // 忽略错误
                    }
                });
            });
        });
    });
}

// 尝试自定义Base64编码
function tryCustomBase64() {
    console.log("\n--- 尝试自定义Base64编码 ---");
    
    // 标准Base64字符集
    const standardBase64 = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
    
    // 可能的自定义字符集
    const customCharsets = [
        "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_", // URL安全
        "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz+/", // 数字在前
        "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789+/" // 小写在前
    ];
    
    testData.forEach((item, index) => {
        console.log(`\n数据组 ${index + 1}: ${item.decoded}`);
        
        // 检查是否使用了标准Base64
        const usedChars = [...new Set(item.decoded)].sort();
        console.log(`使用的字符: ${usedChars.join('')}`);
        
        // 检查字符是否都在标准Base64字符集中
        const isStandardBase64 = usedChars.every(char => standardBase64.includes(char));
        console.log(`是否为标准Base64字符: ${isStandardBase64}`);
    });
}

// 尝试时间相关的算法
function tryTimeBasedAlgorithms() {
    console.log("\n--- 尝试时间相关算法 ---");
    
    // 假设这些数字可能与时间戳相关
    testData.forEach((item, index) => {
        console.log(`\n数据组 ${index + 1}: ${item.number}`);
        
        // 检查是否为时间戳
        const asTimestamp = new Date(item.number * 1000);
        console.log(`作为秒级时间戳: ${asTimestamp.toISOString()}`);
        
        const asMillisTimestamp = new Date(item.number);
        console.log(`作为毫秒级时间戳: ${asMillisTimestamp.toISOString()}`);
        
        // 尝试与当前时间的关系
        const now = Date.now();
        const nowSeconds = Math.floor(now / 1000);
        console.log(`与当前时间差(秒): ${nowSeconds - item.number}`);
        console.log(`与当前时间差(毫秒): ${now - item.number}`);
    });
}

// 尝试数学运算相关的算法
function tryMathBasedAlgorithms() {
    console.log("\n--- 尝试数学运算算法 ---");
    
    testData.forEach((item, index) => {
        console.log(`\n数据组 ${index + 1}: ${item.number}`);
        
        // 尝试各种数学运算
        const operations = [
            item.number,
            item.number * 2,
            item.number + contextData.upuid,
            item.number - contextData.upuid,
            item.number ^ contextData.upuid, // XOR
            item.number % 1000000,
            Math.floor(item.number / 1000),
            item.number.toString(16), // 十六进制
            item.number.toString(36)  // 36进制
        ];
        
        operations.forEach((op, opIndex) => {
            const input = op.toString();
            const md5 = crypto.createHash('md5').update(input).digest('base64');
            
            if (md5.substring(0, 22) === item.decoded) {
                console.log(`🎯 数学运算匹配！`);
                console.log(`  运算: 操作${opIndex + 1}`);
                console.log(`  输入: ${input}`);
                console.log(`  结果: ${md5.substring(0, 22)}`);
            }
        });
    });
}

// 主分析函数
function runAnalysis() {
    analyzeDecodedStrings();
    tryCustomBase64();
    tryTimeBasedAlgorithms();
    tryMathBasedAlgorithms();
    tryServerAlgorithms();
}

// 基于分析结果提供最可能的服务端实现
function provideMostLikelyImplementation() {
    console.log("\n=== 最可能的服务端实现 ===");
    
    console.log(`
基于分析，服务端的生成逻辑最可能是：

1. 输入处理：
   - 主要输入：数字参数 (如 8196230)
   - 可能结合：上下文参数 (ch, upuid等)
   - 可能添加：服务端密钥或盐值

2. 哈希算法：
   - 很可能使用 MD5 或 SHA1
   - 可能使用 HMAC 变体
   - 输出Base64编码

3. 截取逻辑：
   - 取哈希结果的前22位Base64字符
   - 或者使用特定的截取规则

4. 最终组装：
   - 前缀: "iub0c4d3p2"
   - 中间: 22位哈希Base64
   - 后缀: "2"

推荐的复原尝试：
`);

    // 提供一个最可能的实现
    function mostLikelyServerImplementation(number, contextData) {
        // 这里基于分析提供最可能的算法
        const possibleInputs = [
            `${number}_server_salt`,
            `iu_${number}_${contextData.ch}`,
            `task_${number}_${contextData.upuid}`
        ];
        
        const possibleKeys = [
            "pigxpigxpigxpigx",
            "server_secret_key"
        ];
        
        console.log(`\n为数字 ${number} 生成的可能结果：`);
        
        possibleInputs.forEach((input, i) => {
            possibleKeys.forEach((key, j) => {
                const hmac = crypto.createHmac('md5', key).update(input).digest('base64');
                const truncated = hmac.substring(0, 22);
                const full = `iub0c4d3p2${truncated}2`;
                
                console.log(`方案${i+1}-${j+1}: ${full}`);
                console.log(`  输入: ${input}`);
                console.log(`  密钥: ${key}`);
                console.log(`  中间: ${truncated}`);
            });
        });
    }
    
    testData.forEach(item => {
        mostLikelyServerImplementation(item.number, contextData);
    });
}

// 运行所有分析
runAnalysis();
provideMostLikelyImplementation();
