/**
 * 高级逆向工程尝试
 * 尝试更多可能的输入组合和算法
 */

const crypto = require('crypto');

// 目标数据
const targets = [
    { number: 8196230, hex: "f7b696b02b26f788bdf6886107021d22" },
    { number: 8199733, hex: "df3c044c41c841607f3b757698cdc34c" },
    { number: 8201579, hex: "f19e3cc9c35c9596269199d70b246c18" }
];

const contextData = {
    ch: "bx65qqd",
    hqs: "fdj", 
    upuid: 8196926,
    xtp: "0kp"
};

console.log("=== 高级逆向工程尝试 ===");

// 尝试更多可能的密钥
function tryExtendedSecrets() {
    console.log("\n--- 扩展密钥尝试 ---");
    
    // 基于已知信息构造可能的密钥
    const extendedSecrets = [
        // 基础密钥
        'secret',
        'key',
        'salt',
        'password',
        'token',
        'auth',
        'login',
        'user',
        'task',
        'iu',
        
        // 数字相关
        '123456789',
        '987654321',
        '1234567890',
        '0123456789',
        
        // 字母相关
        'abcdefghijklmnopqrstuvwxyz',
        'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
        
        // 混合
        'abc123',
        '123abc',
        'key123',
        'salt456',
        'secret789',
        
        // 基于上下文的变体
        contextData.ch.repeat(2), // bx65qqdbx65qqd
        contextData.ch.repeat(3), // bx65qqdbx65qqdbx65qqd
        contextData.hqs.repeat(3), // fdjfdjfdj
        contextData.ch + '123',
        contextData.hqs + '456',
        
        // 可能的系统相关
        'system_key',
        'app_secret',
        'api_key_2024',
        'encryption_salt',
        'hash_secret',
        
        // 反转的上下文数据
        contextData.ch.split('').reverse().join(''), // dqq56xb
        contextData.hqs.split('').reverse().join(''), // jdf
        
        // 大小写变体
        contextData.ch.toUpperCase(), // BX65QQD
        contextData.hqs.toUpperCase(), // FDJ
        
        // 数字转换
        contextData.upuid.toString(16), // 十六进制
        contextData.upuid.toString(36), // 36进制
        
        // 特殊组合
        `${contextData.ch}_${contextData.hqs}_${contextData.upuid}`,
        `${contextData.upuid}_${contextData.ch}_${contextData.hqs}`,
        
        // 可能的固定字符串
        'iu_generation_key_2024',
        'task_auth_secret',
        'user_token_salt',
        'login_hash_key'
    ];
    
    targets.forEach((target, targetIndex) => {
        console.log(`\n=== 目标 ${targetIndex + 1}: ${target.number} ===`);
        
        let found = false;
        let attempts = 0;
        
        for (const secret of extendedSecrets) {
            if (found) break;
            
            // 尝试不同的输入格式
            const inputFormats = [
                `${secret}${target.number}`,
                `${target.number}${secret}`,
                `${secret}_${target.number}`,
                `${target.number}_${secret}`,
                `${secret}|${target.number}`,
                `${target.number}|${secret}`,
                `${secret}:${target.number}`,
                `${target.number}:${secret}`,
                `${secret}#${target.number}`,
                `${target.number}#${secret}`,
                `${secret}.${target.number}`,
                `${target.number}.${secret}`,
                `${secret}-${target.number}`,
                `${target.number}-${secret}`,
                `${secret}+${target.number}`,
                `${target.number}+${secret}`,
                `${secret}=${target.number}`,
                `${target.number}=${secret}`,
                `${secret}&${target.number}`,
                `${target.number}&${secret}`
            ];
            
            for (const input of inputFormats) {
                attempts++;
                const md5Hash = crypto.createHash('md5').update(input).digest('hex');
                
                if (md5Hash === target.hex) {
                    console.log(`🎯 找到匹配！`);
                    console.log(`  密钥: "${secret}"`);
                    console.log(`  输入: "${input}"`);
                    console.log(`  MD5: ${md5Hash}`);
                    console.log(`  Base64: ${crypto.createHash('md5').update(input).digest('base64')}`);
                    found = true;
                    break;
                }
            }
        }
        
        console.log(`尝试了 ${attempts} 种组合，${found ? '找到' : '未找到'}匹配`);
    });
}

// 尝试基于数字特征的算法
function tryNumberFeatureBasedAlgorithms() {
    console.log("\n--- 基于数字特征的算法 ---");
    
    targets.forEach((target, targetIndex) => {
        console.log(`\n=== 目标 ${targetIndex + 1}: ${target.number} ===`);
        
        const num = target.number;
        const numStr = num.toString();
        
        // 数字特征
        const features = {
            reversed: numStr.split('').reverse().join(''),
            digitSum: numStr.split('').reduce((a, b) => parseInt(a) + parseInt(b), 0),
            digitProduct: numStr.split('').reduce((a, b) => parseInt(a) * parseInt(b), 1),
            length: numStr.length,
            firstDigit: numStr[0],
            lastDigit: numStr[numStr.length - 1],
            middle: numStr.substring(1, numStr.length - 1),
            // 数学运算
            squared: num * num,
            sqrt: Math.floor(Math.sqrt(num)),
            mod1000: num % 1000,
            mod10000: num % 10000,
            div1000: Math.floor(num / 1000),
            // 位运算
            binary: num.toString(2),
            octal: num.toString(8),
            hex: num.toString(16)
        };
        
        console.log(`数字特征:`, features);
        
        // 尝试基于特征的输入
        const featureInputs = [
            features.reversed,
            features.digitSum.toString(),
            features.digitProduct.toString(),
            `${num}_${features.digitSum}`,
            `${features.digitSum}_${num}`,
            `${num}_${features.reversed}`,
            `${features.reversed}_${num}`,
            features.binary,
            features.hex,
            features.octal,
            `${num}_${features.binary}`,
            `${num}_${features.hex}`,
            `${features.hex}_${num}`,
            // 与上下文结合
            `${features.digitSum}_${contextData.ch}`,
            `${contextData.ch}_${features.digitSum}`,
            `${features.hex}_${contextData.ch}`,
            `${contextData.ch}_${features.hex}`
        ];
        
        let found = false;
        featureInputs.forEach((input, inputIndex) => {
            const md5Hash = crypto.createHash('md5').update(input).digest('hex');
            if (md5Hash === target.hex) {
                console.log(`🎯 特征匹配！`);
                console.log(`  输入: "${input}"`);
                console.log(`  特征类型: ${Object.keys(features)[inputIndex] || '组合特征'}`);
                console.log(`  MD5: ${md5Hash}`);
                found = true;
            }
        });
        
        if (!found) {
            console.log(`❌ 特征相关输入未找到匹配`);
        }
    });
}

// 尝试基于字符编码的方法
function tryEncodingBasedMethods() {
    console.log("\n--- 基于字符编码的方法 ---");
    
    targets.forEach((target, targetIndex) => {
        console.log(`\n=== 目标 ${targetIndex + 1}: ${target.number} ===`);
        
        const num = target.number;
        
        // 不同的编码方式
        const encodings = [
            // URL编码
            encodeURIComponent(num.toString()),
            // 字符编码相关
            Buffer.from(num.toString()).toString('hex'),
            Buffer.from(num.toString()).toString('base64'),
            // 可能的转义
            JSON.stringify(num.toString()),
            // Unicode相关
            num.toString().split('').map(c => c.charCodeAt(0)).join(''),
            num.toString().split('').map(c => '\\u' + c.charCodeAt(0).toString(16).padStart(4, '0')).join('')
        ];
        
        let found = false;
        encodings.forEach((encoded, encIndex) => {
            // 尝试编码结果作为输入
            const inputs = [
                encoded,
                `${encoded}_${contextData.ch}`,
                `${contextData.ch}_${encoded}`,
                `salt_${encoded}`,
                `${encoded}_salt`
            ];
            
            inputs.forEach(input => {
                const md5Hash = crypto.createHash('md5').update(input).digest('hex');
                if (md5Hash === target.hex) {
                    console.log(`🎯 编码匹配！`);
                    console.log(`  编码方式: 方法${encIndex + 1}`);
                    console.log(`  编码结果: "${encoded}"`);
                    console.log(`  最终输入: "${input}"`);
                    console.log(`  MD5: ${md5Hash}`);
                    found = true;
                }
            });
        });
        
        if (!found) {
            console.log(`❌ 编码相关输入未找到匹配`);
        }
    });
}

// 尝试基于数据库ID或用户ID的逻辑
function tryDatabaseIdLogic() {
    console.log("\n--- 基于数据库ID逻辑 ---");
    
    targets.forEach((target, targetIndex) => {
        console.log(`\n=== 目标 ${targetIndex + 1}: ${target.number} ===`);
        
        const num = target.number;
        
        // 可能的ID相关逻辑
        const idInputs = [
            // 可能是用户ID
            `user_id_${num}`,
            `uid_${num}`,
            `user_${num}`,
            `id_${num}`,
            
            // 可能是任务ID
            `task_id_${num}`,
            `tid_${num}`,
            `task_${num}`,
            
            // 可能是会话ID
            `session_id_${num}`,
            `sid_${num}`,
            `session_${num}`,
            
            // 可能是订单ID
            `order_id_${num}`,
            `oid_${num}`,
            `order_${num}`,
            
            // 数据库表相关
            `users_${num}`,
            `tasks_${num}`,
            `orders_${num}`,
            `logs_${num}`,
            
            // 带时间戳的ID
            `${num}_${Math.floor(Date.now() / 1000)}`,
            `${num}_${Math.floor(Date.now() / 1000 / 3600)}`, // 小时
            `${num}_${Math.floor(Date.now() / 1000 / 86400)}`, // 天
            
            // 可能的哈希链
            `hash_${num}`,
            `md5_${num}`,
            `sha1_${num}`,
            
            // 可能包含版本信息
            `v1_${num}`,
            `v2_${num}`,
            `version_1_${num}`,
            
            // 可能的应用标识
            `app_${num}`,
            `web_${num}`,
            `mobile_${num}`,
            `client_${num}`
        ];
        
        let found = false;
        idInputs.forEach(input => {
            const md5Hash = crypto.createHash('md5').update(input).digest('hex');
            if (md5Hash === target.hex) {
                console.log(`🎯 ID逻辑匹配！`);
                console.log(`  输入: "${input}"`);
                console.log(`  MD5: ${md5Hash}`);
                found = true;
            }
        });
        
        if (!found) {
            console.log(`❌ ID相关输入未找到匹配`);
        }
    });
}

// 尝试基于网络请求的信息
function tryNetworkBasedInputs() {
    console.log("\n--- 基于网络请求信息 ---");
    
    // 可能包含的网络信息
    const networkInfo = [
        'localhost',
        '127.0.0.1',
        '***********',
        'api.example.com',
        'm.chchapi.cn',
        'task.api.com',
        // User-Agent相关
        'Mozilla',
        'Chrome',
        'Safari',
        'WebKit',
        // 可能的API版本
        'v1',
        'v2',
        'api_v1',
        'api_v2',
        // 端口信息
        '80',
        '443',
        '8080',
        '3000'
    ];
    
    targets.forEach((target, targetIndex) => {
        console.log(`\n=== 目标 ${targetIndex + 1}: ${target.number} ===`);
        
        let found = false;
        
        networkInfo.forEach(info => {
            const networkInputs = [
                `${target.number}_${info}`,
                `${info}_${target.number}`,
                `${target.number}@${info}`,
                `${info}@${target.number}`,
                `${target.number}.${info}`,
                `${info}.${target.number}`,
                `http://${info}/${target.number}`,
                `https://${info}/${target.number}`,
                `${target.number}_${info}_${contextData.ch}`
            ];
            
            networkInputs.forEach(input => {
                const md5Hash = crypto.createHash('md5').update(input).digest('hex');
                if (md5Hash === target.hex) {
                    console.log(`🎯 网络信息匹配！`);
                    console.log(`  网络信息: "${info}"`);
                    console.log(`  输入: "${input}"`);
                    console.log(`  MD5: ${md5Hash}`);
                    found = true;
                }
            });
        });
        
        if (!found) {
            console.log(`❌ 网络相关输入未找到匹配`);
        }
    });
}

// 尝试基于时间的更精确搜索
function tryPreciseTimeSearch() {
    console.log("\n--- 精确时间搜索 ---");
    
    // 基于数字可能代表的时间范围进行搜索
    targets.forEach((target, targetIndex) => {
        console.log(`\n=== 目标 ${targetIndex + 1}: ${target.number} ===`);
        
        // 假设这些数字可能与某个基准时间相关
        const baseTimestamps = [
            1640995200, // 2022-01-01 00:00:00
            1672531200, // 2023-01-01 00:00:00
            1704067200, // 2024-01-01 00:00:00
            1735689600  // 2025-01-01 00:00:00
        ];
        
        let found = false;
        
        baseTimestamps.forEach(baseTime => {
            // 计算可能的时间偏移
            const offset = target.number - baseTime;
            
            const timeInputs = [
                `${target.number}_${baseTime}`,
                `${baseTime}_${target.number}`,
                `${target.number}_${offset}`,
                `${offset}_${target.number}`,
                `time_${target.number}_${baseTime}`,
                `timestamp_${target.number}`,
                `${target.number}_timestamp`,
                // 可能的时间格式
                `${target.number}_${new Date(baseTime * 1000).getFullYear()}`,
                `${target.number}_${new Date(baseTime * 1000).getMonth() + 1}`,
                `${target.number}_${new Date(baseTime * 1000).getDate()}`
            ];
            
            timeInputs.forEach(input => {
                const md5Hash = crypto.createHash('md5').update(input).digest('hex');
                if (md5Hash === target.hex) {
                    console.log(`🎯 时间精确匹配！`);
                    console.log(`  基准时间: ${new Date(baseTime * 1000).toISOString()}`);
                    console.log(`  输入: "${input}"`);
                    console.log(`  MD5: ${md5Hash}`);
                    found = true;
                }
            });
        });
        
        if (!found) {
            console.log(`❌ 精确时间输入未找到匹配`);
        }
    });
}

// 尝试基于已知的pigx密钥的变体
function tryPigxKeyVariants() {
    console.log("\n--- pigx密钥变体尝试 ---");
    
    const pigxVariants = [
        'pigx',
        'pigxpigx',
        'pigxpigxpigx',
        'pigxpigxpigxpigx', // 已知的
        'pigxpigxpigxpigxpigx',
        'pig',
        'pigpig',
        'pigpigpig',
        'pigpigpigpig',
        // 大小写变体
        'PIGX',
        'PIGXPIGX',
        'PIGXPIGXPIGXPIGX',
        'Pigx',
        'PigxPigx',
        // 数字变体
        'pigx123',
        'pigx456',
        'pigx789',
        '123pigx',
        '456pigx',
        '789pigx',
        // 特殊字符
        'pigx_pigx',
        'pigx-pigx',
        'pigx.pigx',
        'pigx@pigx'
    ];
    
    targets.forEach((target, targetIndex) => {
        console.log(`\n=== 目标 ${targetIndex + 1}: ${target.number} ===`);
        
        let found = false;
        
        pigxVariants.forEach(variant => {
            const pigxInputs = [
                `${variant}_${target.number}`,
                `${target.number}_${variant}`,
                `${variant}${target.number}`,
                `${target.number}${variant}`,
                `${variant}_${target.number}_${contextData.ch}`,
                `${target.number}_${variant}_${contextData.ch}`,
                `${contextData.ch}_${variant}_${target.number}`,
                // HMAC尝试
                crypto.createHmac('md5', variant).update(target.number.toString()).digest('hex')
            ];
            
            pigxInputs.forEach(input => {
                if (typeof input === 'string') {
                    const md5Hash = crypto.createHash('md5').update(input).digest('hex');
                    if (md5Hash === target.hex) {
                        console.log(`🎯 pigx变体匹配！`);
                        console.log(`  变体: "${variant}"`);
                        console.log(`  输入: "${input}"`);
                        console.log(`  MD5: ${md5Hash}`);
                        found = true;
                    }
                } else if (input === target.hex) {
                    console.log(`🎯 HMAC匹配！`);
                    console.log(`  HMAC密钥: "${variant}"`);
                    console.log(`  HMAC输入: "${target.number}"`);
                    console.log(`  结果: ${input}`);
                    found = true;
                }
            });
        });
        
        if (!found) {
            console.log(`❌ pigx变体未找到匹配`);
        }
    });
}

// 运行所有高级尝试
console.log("开始高级逆向工程尝试...");
tryExtendedSecrets();
tryNumberFeatureBasedAlgorithms();
tryNetworkBasedInputs();
tryPreciseTimeSearch();
tryPigxKeyVariants();

console.log("\n=== 高级尝试完成 ===");
