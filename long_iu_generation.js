/**
 * 长格式iu生成方法复原尝试
 * 格式：iub0c4d3p2[22位Base64]2
 */

const crypto = require('crypto');

// 已知的长格式iu数据
const longIuData = [
    { number: 8196230, encrypted: "iub0c4d3p2OTdhV3NDc205NGk5OW9oaEJ3SWRJZw2", decoded: "97aWsCsm94i99ohhBwIdIg", hex: "f7b696b02b26f788bdf6886107021d22" },
    { number: 8199733, encrypted: "iub0c4d3p2M3p3RVRFSElRV0JfTzNWMm1NM0RUTQ2", decoded: "3zwETEHIQWB_O3V2mM3DTM", hex: "df3c044c41c841607f3b757698cdc34c" },
    { number: 8201579, encrypted: "iub0c4d3p2OFo0OHljTmNsWllta1puWEN5UnNHRQ2", decoded: "8Z48ycNclZYmkZnXCyRsGE", hex: "f19e3cc9c35c9596269199d70b246c18" }
];

const contextData = {
    ch: "bx65qqd",
    hqs: "fdj", 
    upuid: 8196926,
    xtp: "0kp"
};

console.log("=== 长格式iu生成方法复原 ===");

// 基于新的思路尝试复原
function tryAdvancedPatterns() {
    console.log("\n--- 高级模式尝试 ---");
    
    // 可能的服务端实现思路
    const serverSecrets = [
        // 基于pigx的变体
        'pigxpigxpigxpigx',
        'pigx',
        'pig',
        
        // 可能的系统密钥
        'iub0c4d3p2', // 前缀本身可能是密钥的一部分
        'b0c4d3p2',
        '0c4d3p2',
        'c4d3p2',
        '4d3p2',
        'd3p2',
        '3p2',
        'p2',
        '2',
        
        // 可能的固定盐值
        'salt_key_2024',
        'iu_hash_salt',
        'server_secret',
        'encryption_key',
        
        // 基于上下文的密钥
        contextData.ch + contextData.hqs,
        contextData.ch + contextData.upuid,
        contextData.hqs + contextData.upuid,
        
        // 可能的时间相关密钥
        '20240101',
        '20241228',
        '2024',
        '1228',
        
        // 可能的数字密钥
        '123456',
        '654321',
        '888888',
        '666666',
        '999999'
    ];
    
    longIuData.forEach((item, dataIndex) => {
        console.log(`\n=== 数据组 ${dataIndex + 1}: ${item.number} ===`);
        console.log(`目标MD5: ${item.hex}`);
        
        let found = false;
        
        for (const secret of serverSecrets) {
            if (found) break;
            
            // 尝试不同的输入构造方式
            const inputPatterns = [
                // 基础模式
                `${secret}_${item.number}`,
                `${item.number}_${secret}`,
                `${secret}${item.number}`,
                `${item.number}${secret}`,
                
                // 带上下文
                `${secret}_${item.number}_${contextData.ch}`,
                `${secret}_${contextData.ch}_${item.number}`,
                `${item.number}_${secret}_${contextData.ch}`,
                `${item.number}_${contextData.ch}_${secret}`,
                `${contextData.ch}_${secret}_${item.number}`,
                `${contextData.ch}_${item.number}_${secret}`,
                
                // 复杂组合
                `${secret}_${item.number}_${contextData.ch}_${contextData.upuid}`,
                `${secret}_${contextData.upuid}_${item.number}_${contextData.ch}`,
                `${item.number}_${secret}_${contextData.upuid}_${contextData.ch}`,
                
                // 可能的特殊格式
                `${secret}|${item.number}`,
                `${secret}:${item.number}`,
                `${secret}#${item.number}`,
                `${secret}&${item.number}`,
                `${secret}=${item.number}`,
                `${secret}@${item.number}`,
                
                // 数学运算相关
                `${secret}_${item.number - contextData.upuid}`,
                `${secret}_${item.number + contextData.upuid}`,
                `${secret}_${item.number % 1000000}`,
                
                // HMAC尝试
                crypto.createHmac('md5', secret).update(item.number.toString()).digest('hex'),
                crypto.createHmac('sha1', secret).update(item.number.toString()).digest('hex'),
                crypto.createHmac('md5', secret).update(`${item.number}_${contextData.ch}`).digest('hex'),
                crypto.createHmac('sha1', secret).update(`${item.number}_${contextData.ch}`).digest('hex')
            ];
            
            for (const input of inputPatterns) {
                if (typeof input === 'string') {
                    const md5Hash = crypto.createHash('md5').update(input).digest('hex');
                    if (md5Hash === item.hex) {
                        console.log(`🎯 找到匹配！`);
                        console.log(`  密钥: "${secret}"`);
                        console.log(`  输入: "${input}"`);
                        console.log(`  MD5: ${md5Hash}`);
                        console.log(`  Base64: ${crypto.createHash('md5').update(input).digest('base64')}`);
                        found = true;
                        break;
                    }
                } else if (input === item.hex) {
                    console.log(`🎯 HMAC匹配！`);
                    console.log(`  HMAC密钥: "${secret}"`);
                    console.log(`  结果: ${input}`);
                    found = true;
                    break;
                }
            }
        }
        
        if (!found) {
            console.log(`❌ 未找到匹配的生成方式`);
        }
    });
}

// 尝试基于前缀的特殊逻辑
function tryPrefixBasedLogic() {
    console.log("\n--- 基于前缀的特殊逻辑 ---");
    
    // 分析前缀 "iub0c4d3p2"
    const prefix = "iub0c4d3p2";
    console.log(`前缀: ${prefix}`);
    console.log(`前缀长度: ${prefix.length}`);
    
    // 尝试将前缀作为密钥或盐值的一部分
    const prefixVariants = [
        prefix,
        prefix.substring(2), // "b0c4d3p2"
        prefix.substring(3), // "0c4d3p2"
        prefix.substring(4), // "c4d3p2"
        prefix.substring(5), // "4d3p2"
        prefix.substring(6), // "d3p2"
        prefix.substring(7), // "3p2"
        prefix.substring(8), // "p2"
        prefix.substring(9), // "2"
        
        // 反转
        prefix.split('').reverse().join(''),
        
        // 大小写变体
        prefix.toUpperCase(),
        prefix.toLowerCase(),
        
        // 可能的解码尝试
        // 注意：iub0c4d3p2 可能本身就是某种编码
    ];
    
    longIuData.forEach((item, dataIndex) => {
        console.log(`\n--- 数据组 ${dataIndex + 1} 前缀逻辑测试 ---`);
        
        let found = false;
        
        prefixVariants.forEach((variant, variantIndex) => {
            if (found) return;
            
            const inputs = [
                `${variant}_${item.number}`,
                `${item.number}_${variant}`,
                `${variant}${item.number}`,
                `${item.number}${variant}`,
                `${variant}_${item.number}_${contextData.ch}`,
                `${contextData.ch}_${variant}_${item.number}`
            ];
            
            inputs.forEach(input => {
                const md5Hash = crypto.createHash('md5').update(input).digest('hex');
                if (md5Hash === item.hex) {
                    console.log(`🎯 前缀逻辑匹配！`);
                    console.log(`  前缀变体: "${variant}" (索引${variantIndex})`);
                    console.log(`  输入: "${input}"`);
                    console.log(`  MD5: ${md5Hash}`);
                    found = true;
                }
            });
        });
        
        if (!found) {
            console.log(`❌ 前缀逻辑未找到匹配`);
        }
    });
}

// 尝试基于时间戳的精确搜索
function tryTimestampBasedSearch() {
    console.log("\n--- 基于时间戳的精确搜索 ---");
    
    // 生成可能的时间戳范围
    const now = Math.floor(Date.now() / 1000);
    const possibleTimestamps = [];
    
    // 生成一些可能的时间戳
    for (let i = -86400; i <= 86400; i += 3600) { // 前后一天，每小时一个点
        possibleTimestamps.push(now + i);
    }
    
    // 添加一些固定的时间戳
    possibleTimestamps.push(
        1640995200, // 2022-01-01
        1672531200, // 2023-01-01
        1704067200, // 2024-01-01
        1735689600, // 2025-01-01
        1703721600, // 2023-12-28
        1735257600  // 2024-12-28
    );
    
    longIuData.forEach((item, dataIndex) => {
        console.log(`\n--- 数据组 ${dataIndex + 1} 时间戳搜索 ---`);
        
        let found = false;
        let attempts = 0;
        const maxAttempts = 100; // 限制尝试次数
        
        for (const timestamp of possibleTimestamps) {
            if (found || attempts >= maxAttempts) break;
            
            const timeInputs = [
                `${item.number}_${timestamp}`,
                `${timestamp}_${item.number}`,
                `pigx_${item.number}_${timestamp}`,
                `${item.number}_pigx_${timestamp}`,
                `${contextData.ch}_${item.number}_${timestamp}`,
                `${item.number}_${contextData.ch}_${timestamp}`,
                `salt_${item.number}_${timestamp}`,
                `${item.number}_salt_${timestamp}`
            ];
            
            timeInputs.forEach(input => {
                if (found || attempts >= maxAttempts) return;
                attempts++;
                
                const md5Hash = crypto.createHash('md5').update(input).digest('hex');
                if (md5Hash === item.hex) {
                    console.log(`🎯 时间戳匹配！`);
                    console.log(`  时间戳: ${timestamp} (${new Date(timestamp * 1000).toISOString()})`);
                    console.log(`  输入: "${input}"`);
                    console.log(`  MD5: ${md5Hash}`);
                    found = true;
                }
            });
        }
        
        console.log(`尝试了 ${attempts} 种时间戳组合，${found ? '找到' : '未找到'}匹配`);
    });
}

// 尝试基于数字序列的模式
function trySequencePattern() {
    console.log("\n--- 基于数字序列的模式 ---");
    
    // 分析数字序列
    const numbers = longIuData.map(d => d.number);
    console.log(`数字序列: ${numbers.join(', ')}`);
    
    // 计算序列特征
    const diffs = [];
    for (let i = 1; i < numbers.length; i++) {
        diffs.push(numbers[i] - numbers[i-1]);
    }
    console.log(`差值序列: ${diffs.join(', ')}`);
    
    // 尝试基于序列位置的算法
    longIuData.forEach((item, index) => {
        console.log(`\n--- 数据组 ${index + 1} 序列模式 ---`);
        
        const sequenceInputs = [
            `seq_${index}_${item.number}`,
            `${item.number}_seq_${index}`,
            `index_${index}_${item.number}`,
            `${item.number}_index_${index}`,
            `pos_${index}_${item.number}`,
            `${item.number}_pos_${index}`,
            // 基于差值
            ...(index > 0 ? [
                `diff_${diffs[index-1]}_${item.number}`,
                `${item.number}_diff_${diffs[index-1]}`
            ] : []),
            // 基于累计
            `total_${numbers.slice(0, index + 1).reduce((a, b) => a + b, 0)}_${item.number}`,
            `${item.number}_total_${numbers.slice(0, index + 1).reduce((a, b) => a + b, 0)}`
        ];
        
        let found = false;
        sequenceInputs.forEach(input => {
            const md5Hash = crypto.createHash('md5').update(input).digest('hex');
            if (md5Hash === item.hex) {
                console.log(`🎯 序列模式匹配！`);
                console.log(`  输入: "${input}"`);
                console.log(`  MD5: ${md5Hash}`);
                found = true;
            }
        });
        
        if (!found) {
            console.log(`❌ 序列模式未找到匹配`);
        }
    });
}

// 运行所有尝试
console.log("开始长格式iu生成方法复原...");
tryAdvancedPatterns();
tryPrefixBasedLogic();
tryTimestampBasedSearch();
trySequencePattern();

console.log("\n=== 长格式iu复原尝试完成 ===");
console.log("如果以上方法都没有找到匹配，可能需要：");
console.log("1. 更多的真实数据样本");
console.log("2. 服务端源码或更多上下文信息");
console.log("3. 不同的算法尝试（如其他哈希算法）");
console.log("4. 考虑动态数据（如精确的时间戳、随机数等）");
