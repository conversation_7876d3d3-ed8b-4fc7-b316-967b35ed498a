/**
 * Base64填充分析
 * 发现真实iu值与我们生成的差异在于Base64填充
 */

console.log("=== Base64填充分析 ===");

const realIu = "iuMjgwNDQyMA2";
const realUid = "2804420";

console.log(`真实iu: ${realIu}`);
console.log(`对应uid: ${realUid}`);

// 分析Base64编码
function analyzeBase64Encoding() {
    console.log("\n--- Base64编码分析 ---");
    
    // 标准Base64编码
    const standardBase64 = btoa(realUid);
    console.log(`标准Base64编码: ${standardBase64}`);
    
    // 提取真实iu的中间部分
    const realMiddle = realIu.substring(2, realIu.length - 1);
    console.log(`真实中间部分: ${realMiddle}`);
    
    // 比较
    console.log(`标准编码: ${standardBase64}`);
    console.log(`真实编码: ${realMiddle}`);
    console.log(`差异: 标准编码有填充'=='，真实编码没有`);
    
    // 验证去掉填充后是否匹配
    const withoutPadding = standardBase64.replace(/=/g, '');
    console.log(`去掉填充: ${withoutPadding}`);
    console.log(`匹配: ${withoutPadding === realMiddle ? '✅' : '❌'}`);
    
    if (withoutPadding === realMiddle) {
        console.log("\n🎯 发现规律：iu生成逻辑是 iu + Base64(uid).replace(/=/g, '') + 2");
        return true;
    }
    
    return false;
}

// 验证发现的规律
function verifyPattern() {
    console.log("\n--- 验证发现的规律 ---");
    
    function generateIuFromUid(uid) {
        const base64Uid = btoa(uid.toString()).replace(/=/g, '');
        return `iu${base64Uid}2`;
    }
    
    const generated = generateIuFromUid(realUid);
    console.log(`生成的iu: ${generated}`);
    console.log(`真实的iu: ${realIu}`);
    console.log(`完全匹配: ${generated === realIu ? '✅' : '❌'}`);
    
    if (generated === realIu) {
        console.log("\n🎉 成功复原！iu生成逻辑确认为：");
        console.log("iu + Base64(uid).replace(/=/g, '') + 2");
        
        // 测试其他可能的uid
        console.log("\n--- 测试其他uid ---");
        const testUids = ["1234567", "9876543", "1000000", "2804420"];
        
        testUids.forEach(uid => {
            const testIu = generateIuFromUid(uid);
            console.log(`uid: ${uid} -> iu: ${testIu}`);
        });
        
        return generateIuFromUid;
    }
    
    return null;
}

// 检查更多API请求来确认模式
async function checkMoreApiRequests() {
    console.log("\n--- 检查更多API请求 ---");
    
    // 我们需要查看更多的API请求来确认这个模式
    console.log("需要检查更多API请求文件来确认模式...");
    
    return "需要查看更多API文件";
}

// 运行分析
const patternFound = analyzeBase64Encoding();

if (patternFound) {
    const generatorFunction = verifyPattern();
    
    if (generatorFunction) {
        console.log("\n=== 最终确认的生成函数 ===");
        console.log(`
/**
 * iu生成函数 - 基于真实API数据复原
 * @param {string|number} uid - 用户ID
 * @returns {string} 生成的iu值
 */
function generateIu(uid) {
    // 1. 将uid转换为字符串
    const uidStr = uid.toString();
    
    // 2. Base64编码
    const base64Uid = btoa(uidStr);
    
    // 3. 去掉Base64填充字符
    const withoutPadding = base64Uid.replace(/=/g, '');
    
    // 4. 组装最终结果
    return 'iu' + withoutPadding + '2';
}

// 使用示例
const uid = "2804420";
const iu = generateIu(uid);
console.log('生成的iu:', iu); // 输出: iuMjgwNDQyMA2

// 验证函数
function validateIu(iu, expectedUid) {
    try {
        // 提取中间部分
        const middle = iu.substring(2, iu.length - 1);
        
        // 添加可能需要的填充
        const padded = middle + '='.repeat((4 - middle.length % 4) % 4);
        
        // 解码
        const decoded = atob(padded);
        
        return decoded === expectedUid.toString();
    } catch (e) {
        return false;
    }
}
`);
    }
}

checkMoreApiRequests();
